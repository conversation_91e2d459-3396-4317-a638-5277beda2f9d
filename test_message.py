#!/usr/bin/env python3
"""
Test sending WhatsApp message after authentication.
"""

import sys
import os
import json
import time

# Add the integrations directory to the path
sys.path.append('integrations')

from whatsapp_integration.whatsapp_api import WhatsAppMessaging

def load_config():
    """Load API key from config file."""
    try:
        with open('integrations/whatsapp_integration/config.json', 'r') as f:
            config = json.load(f)
            return config.get('api_key')
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return None

def load_account_id():
    """Load account ID from saved file."""
    try:
        with open('whatsapp_account.txt', 'r') as f:
            for line in f:
                if line.startswith('Account ID:'):
                    return line.split(':', 1)[1].strip()
    except Exception as e:
        print(f"❌ Error loading account ID: {e}")
    return None

def main():
    """Test messaging with existing account."""
    print("📤 WhatsApp Message Test")
    print("=" * 30)
    
    # Load API key
    api_key = load_config()
    if not api_key:
        print("❌ No API key found in config.json")
        return
    
    # Load account ID
    account_id = load_account_id()
    if not account_id:
        print("❌ No account ID found. Run generate_qr.py first.")
        return
    
    print(f"✅ API key loaded: {api_key[:10]}...")
    print(f"✅ Account ID loaded: {account_id}")
    
    try:
        # Initialize WhatsApp with existing account
        print("\n🔧 Initializing WhatsApp...")
        whatsapp = WhatsAppMessaging(unipile_api_key=api_key)
        whatsapp.unipile.account_id = account_id  # Use existing account
        print("✅ WhatsApp initialized with existing account!")
        
        # Check account status
        print("\n📊 Checking account status...")
        try:
            status = whatsapp.unipile.check_account_status()
            print(f"✅ Account Status: {status}")
            
            if status.get('object') == 'Account':
                print("🎉 Account is authenticated and ready!")
                
                # Test sending message
                phone = input("\n📞 Enter phone number to test (with country code, e.g., +**********): ").strip()
                if phone:
                    message = input("💬 Enter message (or press Enter for default): ").strip()
                    if not message:
                        message = "🎉 Hello! This is a test message from WhatsApp API integration. Your structure is working perfectly!"
                    
                    print(f"\n📤 Sending message to {phone}...")
                    result = whatsapp.send_message(phone, message)
                    print("✅ Message sent successfully!")
                    print(f"📋 Response: {result}")
                    
                    # Test bulk messaging
                    bulk_test = input("\n🔄 Test bulk messaging? (y/n): ").strip().lower()
                    if bulk_test == 'y':
                        recipients = [phone]  # Use same number for test
                        bulk_message = "🚀 This is a bulk message test from WhatsApp API!"
                        
                        print(f"\n📤 Sending bulk message...")
                        bulk_results = whatsapp.send_bulk_messages(recipients, bulk_message)
                        print("✅ Bulk message sent!")
                        print(f"📋 Results: {bulk_results}")
                
            else:
                print("⏳ Account not yet authenticated.")
                print("📱 Please scan the QR code first using:")
                print("   1. The web interface: integrations/whatsapp_integration/whatsapp.html")
                print("   2. Or the QR URL from whatsapp_account.txt")
                
        except Exception as e:
            print(f"⏳ Account status check failed: {e}")
            print("📱 This usually means the QR code hasn't been scanned yet.")
            print("   Please scan the QR code using the web interface or QR URL.")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
