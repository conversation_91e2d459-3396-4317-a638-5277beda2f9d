<!DOCTYPE html>
<html>
<head>
  <title>Connect Instagram with Unipile</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 50px auto;
      padding: 20px;
      background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
      min-height: 100vh;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 15px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .instagram-logo {
      background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-size: 28px;
      font-weight: bold;
    }
    button {
      background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      font-size: 16px;
      border-radius: 25px;
      cursor: pointer;
      margin: 10px 5px;
      min-width: 150px;
      transition: transform 0.2s;
    }
    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
    button:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
    }
    .error {
      color: #dc2743;
      margin-top: 10px;
      padding: 10px;
      background-color: #ffe6e6;
      border-radius: 10px;
      border-left: 4px solid #dc2743;
    }
    .success {
      color: #27ae60;
      margin-top: 10px;
      padding: 10px;
      background-color: #e8f5e8;
      border-radius: 10px;
      border-left: 4px solid #27ae60;
    }
    .warning {
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      color: #856404;
      padding: 15px;
      border-radius: 10px;
      margin-bottom: 20px;
    }
    .config-section {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 10px;
      margin-bottom: 20px;
    }
    input[type="text"], input[type="password"], textarea {
      width: 100%;
      padding: 12px;
      margin: 8px 0;
      border: 2px solid #e1e8ed;
      border-radius: 10px;
      box-sizing: border-box;
      transition: border-color 0.3s;
    }
    input[type="text"]:focus, input[type="password"]:focus, textarea:focus {
      border-color: #e6683c;
      outline: none;
    }
    textarea {
      height: 120px;
      resize: vertical;
    }
    .section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #e0e0e0;
      border-radius: 15px;
      background: #fafafa;
    }
    .section h3 {
      margin-top: 0;
      color: #dc2743;
    }
    .account-info {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      border-radius: 15px;
      margin: 15px 0;
    }
    .button-group {
      text-align: center;
      margin: 20px 0;
    }
    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin: 20px 0;
    }
    .feature-card {
      background: white;
      padding: 15px;
      border-radius: 10px;
      border: 2px solid #e1e8ed;
      text-align: center;
    }
    .feature-card:hover {
      border-color: #e6683c;
      transform: translateY(-2px);
      transition: all 0.3s;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1><span class="instagram-logo">Instagram</span> Integration with Unipile</h1>
      <p>Connect your Instagram account for automated posting, messaging, and analytics</p>
    </div>
    
    <div class="warning">
      <strong>⚠️ Security Notice:</strong> Never expose your API key in client-side code. 
      This is for development/testing only. In production, use a backend server to handle API calls.
    </div>

    <div class="config-section">
      <h3>🔧 Configuration</h3>
      <label for="apiKey">API Key:</label>
      <input type="text" id="apiKey" value="zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=" placeholder="Enter your Unipile API key" />
      
      <label for="dsn">DSN:</label>
      <input type="text" id="dsn" value="https://api1.unipile.com:13115" placeholder="Enter your DSN" />
    </div>

    <!-- Authentication Section -->
    <div class="section">
      <h3>1. 🔐 Connect Instagram Account</h3>
      <p>Enter your Instagram credentials to connect your account:</p>
      
      <div style="margin-bottom: 20px;">
        <label for="username">Instagram Username:</label>
        <input type="text" id="username" placeholder="Enter your Instagram username" />
        
        <label for="password">Instagram Password:</label>
        <input type="password" id="password" placeholder="Enter your Instagram password" />
        
        <div class="button-group">
          <button onclick="authenticateWithCredentials()">🔗 Connect Instagram Account</button>
        </div>
      </div>
      
      <div id="authContainer"></div>
    </div>

    <!-- Account Status Section -->
    <div class="section">
      <h3>2. 📊 Check Account Status</h3>
      <p>Verify your Instagram account connection and status.</p>
      <div class="button-group">
        <button onclick="checkAccountStatus()">📋 Check Status</button>
      </div>
      <div id="statusContainer"></div>
    </div>

    <!-- Features Grid -->
    <div class="section">
      <h3>3. 🚀 Instagram Features</h3>
      <div class="feature-grid">
        
        <!-- Direct Messages -->
        <div class="feature-card">
          <h4>💬 Direct Messages</h4>
          <label for="dmRecipient">To:</label>
          <input type="text" id="dmRecipient" placeholder="Username or User ID" />
          <label for="dmMessage">Message:</label>
          <textarea id="dmMessage" placeholder="Enter your message..."></textarea>
          <button onclick="sendDirectMessage()">📤 Send DM</button>
          <div id="dmContainer"></div>
        </div>

        <!-- Post Content -->
        <div class="feature-card">
          <h4>📸 Post Content</h4>
          <label for="postCaption">Caption:</label>
          <textarea id="postCaption" placeholder="Enter your post caption...

#instagram #unipile #automation"></textarea>
          <label for="imageUrl">Image URL (optional):</label>
          <input type="text" id="imageUrl" placeholder="https://example.com/image.jpg" />
          <button onclick="postContent()">📱 Post to Instagram</button>
          <div id="postContainer"></div>
        </div>

        <!-- Profile Info -->
        <div class="feature-card">
          <h4>👤 Profile Info</h4>
          <p>Get your Instagram profile information and statistics.</p>
          <button onclick="getProfile()">📊 Get Profile</button>
          <div id="profileContainer"></div>
        </div>

        <!-- Followers -->
        <div class="feature-card">
          <h4>👥 Followers</h4>
          <p>Retrieve your followers list and analytics.</p>
          <button onclick="getFollowers()">📈 Get Followers</button>
          <div id="followersContainer"></div>
        </div>

      </div>
    </div>

    <div id="message"></div>
  </div>

  <script>
    let currentAccountId = null;

    async function makeAPICall(endpoint, method = 'GET', body = null, additionalHeaders = {}) {
      const apiKey = document.getElementById('apiKey').value.trim();
      const dsn = document.getElementById('dsn').value.trim();
      
      if (!apiKey || !dsn) {
        throw new Error('Please enter both API key and DSN');
      }

      const headers = {
        'X-API-KEY': apiKey,
        'accept': 'application/json',
        ...additionalHeaders
      };

      if (method !== 'GET') {
        headers['Content-Type'] = 'application/json';
      }

      const config = {
        method: method,
        headers: headers
      };

      if (body) {
        config.body = JSON.stringify(body);
      }

      const response = await fetch(`${dsn}${endpoint}`, config);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return await response.json();
    }

    async function authenticateWithCredentials() {
      const container = document.getElementById('authContainer');
      const username = document.getElementById('username').value.trim();
      const password = document.getElementById('password').value.trim();
      
      try {
        if (!username || !password) {
          throw new Error('Please enter both username and password');
        }

        container.innerHTML = '<p>🔄 Authenticating with Instagram...</p>';

        const data = await makeAPICall('/api/v1/accounts', 'POST', {
          provider: 'INSTAGRAM',
          username: username,
          password: password
        });
        
        console.log('Auth Response:', data);
        currentAccountId = data.account_id;

        if (data.object === 'Account') {
          container.innerHTML = `
            <div class="account-info">
              <h4>✅ Instagram Authentication Successful!</h4>
              <p><strong>Account ID:</strong> ${currentAccountId}</p>
              <p><strong>Username:</strong> ${username}</p>
              <p><strong>Status:</strong> Connected</p>
              <details style="margin-top: 10px;">
                <summary>Full Response Data</summary>
                <pre style="font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
              </details>
            </div>
          `;
          showMessage('Instagram account connected successfully!', 'success');
        } else if (data.object === 'Checkpoint') {
          const checkpointType = data.checkpoint?.type || 'Unknown';
          container.innerHTML = `
            <div class="account-info" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
              <h4>⚠️ Instagram Checkpoint Required</h4>
              <p><strong>Type:</strong> ${checkpointType}</p>
              <p><strong>Account ID:</strong> ${data.account_id}</p>
              ${checkpointType === 'CAPTCHA' ? `
                <p>CAPTCHA verification required. Contact Unipile support for assistance.</p>
              ` : checkpointType === '2FA' ? `
                <p>Two-factor authentication required. Check your phone for verification code.</p>
              ` : `
                <p>Additional verification required: ${checkpointType}</p>
              `}
            </div>
          `;
          showMessage(`Instagram requires ${checkpointType} verification`, 'error');
        }
        
        // Clear password field for security
        document.getElementById('password').value = '';

      } catch (error) {
        console.error('Auth Error:', error);
        showMessage(`Failed to authenticate: ${error.message}`, 'error');
        container.innerHTML = `
          <div class="error">
            <h4>❌ Authentication Failed</h4>
            <p>${error.message}</p>
          </div>
        `;
      }
    }

    async function checkAccountStatus() {
      const container = document.getElementById('statusContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('No account ID available. Please authenticate first.');
        }

        container.innerHTML = '<p>🔄 Checking account status...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}`);
        console.log('Status Response:', data);

        if (data.object === 'Account') {
          const sources = data.sources || [];
          const status = sources.length > 0 ? sources[0].status : 'Unknown';
          
          container.innerHTML = `
            <div class="account-info">
              <h4>✅ Account Status: ${status}</h4>
              <p><strong>Type:</strong> ${data.type}</p>
              <p><strong>Name:</strong> ${data.name}</p>
              <p><strong>ID:</strong> ${data.id}</p>
              <p><strong>Created:</strong> ${new Date(data.created_at).toLocaleString()}</p>
              <p><strong>Sources:</strong> ${sources.length} available</p>
            </div>
          `;
          showMessage(`Account status: ${status}`, status === 'OK' ? 'success' : 'error');
        } else {
          container.innerHTML = `
            <div class="account-info">
              <h4>Account Information</h4>
              <pre>${JSON.stringify(data, null, 2)}</pre>
            </div>
          `;
        }

      } catch (error) {
        console.error('Status Error:', error);
        if (error.message.includes('404')) {
          container.innerHTML = `
            <div class="error">
              <h4>❌ Account Not Found</h4>
              <p>Please authenticate again.</p>
            </div>
          `;
        } else {
          container.innerHTML = `
            <div class="error">
              <h4>❌ Error Checking Status</h4>
              <p>${error.message}</p>
            </div>
          `;
        }
        showMessage(`Failed to check status: ${error.message}`, 'error');
      }
    }

    async function sendDirectMessage() {
      const container = document.getElementById('dmContainer');
      const recipient = document.getElementById('dmRecipient').value.trim();
      const message = document.getElementById('dmMessage').value.trim();
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }
        if (!recipient || !message) {
          throw new Error('Please enter both recipient and message.');
        }

        container.innerHTML = '<p>📤 Sending direct message...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/messages`, 'POST', {
          to: recipient,
          text: message
        });
        
        container.innerHTML = `
          <div class="success">
            <h5>✅ Message Sent!</h5>
            <p><strong>To:</strong> ${recipient}</p>
            <p><strong>Message:</strong> "${message}"</p>
          </div>
        `;
        
        showMessage('Direct message sent successfully!', 'success');
        document.getElementById('dmMessage').value = '';

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Send</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to send message: ${error.message}`, 'error');
      }
    }

    async function postContent() {
      const container = document.getElementById('postContainer');
      const caption = document.getElementById('postCaption').value.trim();
      const imageUrl = document.getElementById('imageUrl').value.trim();
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }
        if (!caption) {
          throw new Error('Please enter a caption.');
        }

        container.innerHTML = '<p>📱 Posting to Instagram...</p>';

        const payload = { text: caption };
        if (imageUrl) {
          payload.image_url = imageUrl;
        }

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/posts`, 'POST', payload);
        
        container.innerHTML = `
          <div class="success">
            <h5>✅ Posted Successfully!</h5>
            <p><strong>Caption:</strong> "${caption}"</p>
            ${imageUrl ? `<p><strong>Image:</strong> ${imageUrl}</p>` : ''}
          </div>
        `;
        
        showMessage('Content posted to Instagram successfully!', 'success');
        document.getElementById('postCaption').value = '';
        document.getElementById('imageUrl').value = '';

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Post</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to post content: ${error.message}`, 'error');
      }
    }

    async function getProfile() {
      const container = document.getElementById('profileContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }

        container.innerHTML = '<p>👤 Getting profile info...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/profile`);
        
        container.innerHTML = `
          <div class="success">
            <h5>✅ Profile Retrieved</h5>
            <p><strong>Username:</strong> ${data.username || 'N/A'}</p>
            <p><strong>Followers:</strong> ${data.followers_count || 'N/A'}</p>
            <p><strong>Following:</strong> ${data.following_count || 'N/A'}</p>
            <p><strong>Posts:</strong> ${data.posts_count || 'N/A'}</p>
          </div>
        `;
        
        showMessage('Profile information retrieved!', 'success');

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Get Profile</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to get profile: ${error.message}`, 'error');
      }
    }

    async function getFollowers() {
      const container = document.getElementById('followersContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }

        container.innerHTML = '<p>👥 Getting followers...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/followers`);
        
        const followers = data.followers || data.items || [];
        container.innerHTML = `
          <div class="success">
            <h5>✅ Followers Retrieved</h5>
            <p><strong>Count:</strong> ${followers.length}</p>
            ${followers.length > 0 ? `<p>Recent followers loaded</p>` : '<p>No followers data available</p>'}
          </div>
        `;
        
        showMessage('Followers information retrieved!', 'success');

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Get Followers</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to get followers: ${error.message}`, 'error');
      }
    }

    function showMessage(text, type) {
      const messageDiv = document.getElementById('message');
      messageDiv.textContent = text;
      messageDiv.className = type;
      
      // Auto-clear message after 5 seconds
      setTimeout(() => {
        messageDiv.textContent = '';
        messageDiv.className = '';
      }, 5000);
    }

    // Auto-focus on username input
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('username').focus();
    });
  </script>
</body>
</html>
