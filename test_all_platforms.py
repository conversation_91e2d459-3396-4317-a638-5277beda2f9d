#!/usr/bin/env python3
"""
Test all social media platforms with the updated API key.
"""

import sys
import os
import json
import time

# Add the integrations directory to the path
sys.path.append('integrations')

from whatsapp_integration.whatsapp_api import WhatsAppMessaging

def load_config(platform):
    """Load API key from platform config file."""
    try:
        config_path = f'integrations/{platform}_integration/config.json'
        with open(config_path, 'r') as f:
            config = json.load(f)
            return config.get('api_key')
    except Exception as e:
        print(f"❌ Error loading {platform} config: {e}")
        return None

def test_api_connection():
    """Test API connection using curl."""
    print("🔗 Testing API Connection")
    print("=" * 30)
    
    api_key = load_config('whatsapp')
    if not api_key:
        print("❌ No API key found")
        return False
    
    print(f"✅ API Key: {api_key[:15]}...")
    
    # Test with curl command
    import subprocess
    try:
        result = subprocess.run([
            'curl', '--request', 'GET', 
            '--url', 'https://api1.unipile.com:13115/api/v1/accounts',
            '--header', f'X-API-KEY:{api_key}',
            '--header', 'accept: application/json'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ API connection successful!")
            print(f"📋 Response: {result.stdout[:200]}...")
            return True
        else:
            print(f"❌ API connection failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False

def test_whatsapp_structure():
    """Test WhatsApp with your desired structure."""
    print("\n📱 Testing WhatsApp Integration")
    print("=" * 35)
    
    api_key = load_config('whatsapp')
    if not api_key:
        print("❌ No WhatsApp API key found")
        return False
    
    try:
        # Test your desired structure
        print("🔧 Initializing WhatsAppMessaging...")
        whatsapp = WhatsAppMessaging(unipile_api_key=api_key)
        print("✅ WhatsAppMessaging initialized!")
        
        # Check existing accounts
        try:
            existing_accounts = whatsapp.unipile.session.get(
                f"{whatsapp.unipile.dsn}/api/v1/accounts"
            ).json()
            
            if existing_accounts.get('items'):
                print(f"📱 Found {len(existing_accounts['items'])} existing account(s)")
                for account in existing_accounts['items']:
                    if account.get('type') == 'WHATSAPP':
                        print(f"   • WhatsApp: {account.get('name')} (ID: {account.get('id')})")
                        whatsapp.unipile.account_id = account.get('id')
                        
                        # Test sending message to the connected number
                        phone = account.get('connection_params', {}).get('im', {}).get('phone_number')
                        if phone:
                            test_msg = input(f"\n📤 Send test message to {phone}? (y/n): ").strip().lower()
                            if test_msg == 'y':
                                message = "🎉 WhatsApp API test successful! Your structure is working perfectly!"
                                try:
                                    result = whatsapp.send_message(f"+{phone}", message)
                                    print("✅ Test message sent successfully!")
                                    print(f"📋 Response: {result}")
                                except Exception as e:
                                    print(f"❌ Failed to send message: {e}")
            else:
                print("📱 No existing accounts found. Creating new one...")
                auth_result = whatsapp.authenticate_account()
                print(f"📱 New account created: {auth_result.get('account_id')}")
                print("📱 Please scan the QR code to authenticate")
        
        except Exception as e:
            print(f"⚠️ Error checking accounts: {e}")
        
        # Show available methods
        print(f"\n📋 Available WhatsApp methods:")
        methods = [method for method in dir(whatsapp) if not method.startswith('_') and callable(getattr(whatsapp, method))]
        for method in methods:
            print(f"   • {method}()")
        
        return True
        
    except Exception as e:
        print(f"❌ WhatsApp test failed: {e}")
        return False

def verify_all_configs():
    """Verify all platform configs have the correct API key."""
    print("\n🔧 Verifying All Platform Configurations")
    print("=" * 45)
    
    platforms = ['whatsapp', 'telegram', 'facebook', 'instagram', 'tiktok', 'linkedin']
    target_api_key = "b6hDoV4N.fi7MF5SvQ1g7tH8CEr6TpExQHoXbV8tKH/qQseSKN5s="
    
    all_updated = True
    
    for platform in platforms:
        api_key = load_config(platform)
        if api_key == target_api_key:
            print(f"✅ {platform.title()}: API key updated correctly")
        else:
            print(f"❌ {platform.title()}: API key mismatch")
            print(f"   Expected: {target_api_key[:20]}...")
            print(f"   Found: {api_key[:20] if api_key else 'None'}...")
            all_updated = False
    
    return all_updated

def main():
    """Main test function."""
    print("🧪 Multi-Platform API Test")
    print("=" * 50)
    print("Testing all social media integrations with updated API key")
    print(f"API Key: b6hDoV4N.fi7MF5SvQ1g7tH8CEr6TpExQHoXbV8tKH/qQseSKN5s=")
    
    # Step 1: Verify configurations
    if not verify_all_configs():
        print("\n❌ Configuration verification failed!")
        return
    
    # Step 2: Test API connection
    if not test_api_connection():
        print("\n❌ API connection test failed!")
        return
    
    # Step 3: Test WhatsApp structure
    if not test_whatsapp_structure():
        print("\n❌ WhatsApp structure test failed!")
        return
    
    print("\n🎉 All tests completed successfully!")
    print("\n📋 Summary:")
    print("✅ API key updated across all platforms")
    print("✅ API connection working")
    print("✅ WhatsApp structure functioning")
    print("✅ Ready for messaging tests")
    
    print("\n🚀 Next steps:")
    print("1. Use the web interfaces for each platform")
    print("2. Test messaging with your WhatsAppMessaging structure")
    print("3. Try the unified messaging system")

if __name__ == "__main__":
    main()
