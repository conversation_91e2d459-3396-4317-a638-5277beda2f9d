<!DOCTYPE html>
<html>
<head>
  <title>Connect LinkedIn with Unipile</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 50px auto;
      padding: 20px;
      background-color: #f8f9fa;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .linkedin-logo {
      color: #0077B5;
      font-size: 24px;
      font-weight: bold;
    }
    button {
      background-color: #0077B5;
      color: white;
      border: none;
      padding: 12px 24px;
      font-size: 16px;
      border-radius: 5px;
      cursor: pointer;
      margin: 10px 5px;
      min-width: 150px;
    }
    button:hover {
      background-color: #005885;
    }
    button:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
    .error {
      color: red;
      margin-top: 10px;
      padding: 10px;
      background-color: #ffe6e6;
      border-radius: 5px;
    }
    .success {
      color: green;
      margin-top: 10px;
      padding: 10px;
      background-color: #e6ffe6;
      border-radius: 5px;
    }
    .warning {
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      color: #856404;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .config-section {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    input[type="text"], textarea {
      width: 100%;
      padding: 8px;
      margin: 5px 0;
      border: 1px solid #ddd;
      border-radius: 3px;
      box-sizing: border-box;
    }
    textarea {
      height: 100px;
      resize: vertical;
    }
    .section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #e0e0e0;
      border-radius: 5px;
    }
    .section h3 {
      margin-top: 0;
      color: #0077B5;
    }
    .oauth-container {
      text-align: center;
      padding: 20px;
      background-color: #f0f8ff;
      border-radius: 5px;
      margin: 20px 0;
    }
    .account-info {
      background-color: #e8f5e8;
      padding: 15px;
      border-radius: 5px;
      margin: 10px 0;
    }
    .button-group {
      text-align: center;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1><span class="linkedin-logo">LinkedIn</span> Integration with Unipile</h1>
    </div>
    
    <div class="warning">
      <strong>⚠️ Security Notice:</strong> Never expose your API key in client-side code. 
      This is for development/testing only. In production, use a backend server to handle API calls.
    </div>

    <div class="config-section">
      <h3>Configuration</h3>
      <label for="apiKey">API Key:</label>
      <input type="text" id="apiKey" value="zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=" placeholder="Enter your Unipile API key" />
      
      <label for="dsn">DSN:</label>
      <input type="text" id="dsn" value="https://api1.unipile.com:13115" placeholder="Enter your DSN" />
    </div>

    <!-- Authentication Section -->
    <div class="section">
      <h3>1. Connect LinkedIn Account</h3>
      <p>Enter your LinkedIn credentials to connect your account:</p>

      <div style="margin-bottom: 20px;">
        <label for="username">LinkedIn Username/Email:</label>
        <input type="text" id="username" placeholder="Enter your LinkedIn username or email" />

        <label for="password">LinkedIn Password:</label>
        <input type="password" id="password" placeholder="Enter your LinkedIn password" />

        <div class="button-group">
          <button onclick="authenticateWithCredentials()">Connect LinkedIn Account</button>
        </div>
      </div>

      <div id="authContainer"></div>
    </div>

    <!-- Account Status Section -->
    <div class="section">
      <h3>2. Check Account Status</h3>
      <p>Check if your LinkedIn account is connected and active.</p>
      <div class="button-group">
        <button onclick="checkAccountStatus()">Check Status</button>
      </div>
      <div id="statusContainer"></div>

      <div style="margin-top: 15px; padding: 10px; background-color: #e8f4fd; border-radius: 5px; font-size: 14px;">
        <strong>💡 Status Guide:</strong>
        <ul style="margin: 5px 0; padding-left: 20px;">
          <li><strong>OK:</strong> Account fully authenticated and ready to use</li>
          <li><strong>CAPTCHA:</strong> Requires CAPTCHA completion (contact Unipile support)</li>
          <li><strong>2FA:</strong> Requires two-factor authentication code</li>
          <li><strong>Not Found:</strong> Account removed or authentication failed</li>
        </ul>
      </div>
    </div>

    <!-- Post Content Section -->
    <div class="section">
      <h3>3. Post Content to LinkedIn</h3>
      <p>Share content on your LinkedIn profile.</p>
      <label for="postContent">Content to post:</label>
      <textarea id="postContent" placeholder="Enter your LinkedIn post content here...

Example:
🚀 Excited to share my latest project! Just integrated LinkedIn API with Unipile for seamless social media automation.

#LinkedIn #API #Automation #TechInnovation"></textarea>
      <div class="button-group">
        <button onclick="postContent()">Post to LinkedIn</button>
      </div>
      <div id="postContainer"></div>

      <div style="margin-top: 15px; padding: 10px; background-color: #fff3cd; border-radius: 5px; font-size: 14px;">
        <strong>⚠️ Posting Requirements:</strong>
        <ul style="margin: 5px 0; padding-left: 20px;">
          <li>Account must be fully authenticated (Status: OK)</li>
          <li>Content should follow LinkedIn community guidelines</li>
          <li>Avoid excessive posting to prevent rate limiting</li>
          <li>Test with simple text posts first</li>
        </ul>
      </div>
    </div>

    <!-- Profile Info Section -->
    <div class="section">
      <h3>4. Get Profile Information</h3>
      <p>Retrieve your LinkedIn profile information.</p>
      <div class="button-group">
        <button onclick="getProfile()">Get Profile Info</button>
      </div>
      <div id="profileContainer"></div>
    </div>

    <div id="message"></div>
  </div>

  <script>
    let currentAccountId = null;

    async function makeAPICall(endpoint, method = 'GET', body = null, additionalHeaders = {}) {
      const apiKey = document.getElementById('apiKey').value.trim();
      const dsn = document.getElementById('dsn').value.trim();

      if (!apiKey || !dsn) {
        throw new Error('Please enter both API key and DSN');
      }

      const headers = {
        'X-API-KEY': apiKey,
        'accept': 'application/json',
        ...additionalHeaders
      };

      if (method !== 'GET') {
        headers['Content-Type'] = 'application/json';
      }

      const config = {
        method: method,
        headers: headers
      };

      if (body) {
        config.body = JSON.stringify(body);
      }

      const response = await fetch(`${dsn}${endpoint}`, config);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return await response.json();
    }

    async function authenticateWithCredentials() {
      const container = document.getElementById('authContainer');
      const username = document.getElementById('username').value.trim();
      const password = document.getElementById('password').value.trim();

      try {
        if (!username || !password) {
          throw new Error('Please enter both username and password');
        }

        container.innerHTML = '<p>Authenticating with LinkedIn...</p>';

        const data = await makeAPICall('/api/v1/accounts', 'POST', {
          provider: 'LINKEDIN',
          username: username,
          password: password
        });

        console.log('Auth Response:', data);
        currentAccountId = data.account_id;

        container.innerHTML = `
          <div class="account-info">
            <h4>✅ LinkedIn Authentication Successful!</h4>
            <p><strong>Account ID:</strong> ${currentAccountId}</p>
            <pre>${JSON.stringify(data, null, 2)}</pre>
          </div>
        `;

        showMessage('LinkedIn account connected successfully!', 'success');

        // Clear password field for security
        document.getElementById('password').value = '';

      } catch (error) {
        console.error('Auth Error:', error);
        showMessage(`Failed to authenticate: ${error.message}`, 'error');
        container.innerHTML = '';
      }
    }



    async function checkAccountStatus() {
      const container = document.getElementById('statusContainer');

      try {
        if (!currentAccountId) {
          throw new Error('No account ID available. Please authenticate first.');
        }

        container.innerHTML = '<p>Checking account status...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}`);
        console.log('Status Response:', data);

        // Check if account is fully authenticated
        if (data.object === 'Account') {
          const sources = data.sources || [];
          const status = sources.length > 0 ? sources[0].status : 'Unknown';

          container.innerHTML = `
            <div class="account-info">
              <h4>✅ Account Status: ${status}</h4>
              <p><strong>Account Type:</strong> ${data.type}</p>
              <p><strong>Account Name:</strong> ${data.name}</p>
              <p><strong>Account ID:</strong> ${data.id}</p>
              <p><strong>Created:</strong> ${new Date(data.created_at).toLocaleString()}</p>
              <p><strong>Sources:</strong> ${sources.length} available</p>

              <details style="margin-top: 10px;">
                <summary>Full Response Data</summary>
                <pre style="font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
              </details>
            </div>
          `;

          showMessage(`Account status: ${status}`, status === 'OK' ? 'success' : 'error');

        } else if (data.object === 'Checkpoint') {
          // Handle checkpoint state
          const checkpointType = data.checkpoint?.type || 'Unknown';

          container.innerHTML = `
            <div class="account-info" style="background-color: #fff3cd;">
              <h4>⚠️ Account in Checkpoint State</h4>
              <p><strong>Checkpoint Type:</strong> ${checkpointType}</p>
              <p><strong>Account ID:</strong> ${data.account_id}</p>

              ${checkpointType === 'CAPTCHA' ? `
                <p><strong>Status:</strong> CAPTCHA verification required</p>
                <p>Contact Unipile support for CAPTCHA library access.</p>
              ` : checkpointType === '2FA' ? `
                <p><strong>Status:</strong> Two-factor authentication required</p>
                <p>Check your phone/email for verification code.</p>
              ` : `
                <p><strong>Status:</strong> Additional verification required</p>
              `}

              <details style="margin-top: 10px;">
                <summary>Full Response Data</summary>
                <pre style="font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
              </details>
            </div>
          `;

          showMessage(`Account in ${checkpointType} checkpoint state`, 'error');

        } else {
          container.innerHTML = `
            <div class="account-info">
              <h4>Account Information</h4>
              <pre>${JSON.stringify(data, null, 2)}</pre>
            </div>
          `;
          showMessage('Account status retrieved', 'success');
        }

      } catch (error) {
        console.error('Status Error:', error);

        if (error.message.includes('404')) {
          container.innerHTML = `
            <div class="error">
              <h4>❌ Account Not Found</h4>
              <p>Account ID: ${currentAccountId}</p>
              <p>The account may have been removed due to failed authentication or timeout.</p>
              <p>Please try authenticating again.</p>
            </div>
          `;
          showMessage('Account not found - please authenticate again', 'error');
        } else {
          container.innerHTML = `
            <div class="error">
              <h4>❌ Error Checking Status</h4>
              <p>${error.message}</p>
            </div>
          `;
          showMessage(`Failed to check status: ${error.message}`, 'error');
        }
      }
    }

    async function postContent() {
      const container = document.getElementById('postContainer');
      const content = document.getElementById('postContent').value.trim();

      try {
        if (!currentAccountId) {
          throw new Error('No account ID available. Please authenticate first.');
        }

        if (!content) {
          throw new Error('Please enter content to post.');
        }

        container.innerHTML = '<p>Posting content to LinkedIn...</p>';

        // Try different API endpoints for posting
        let data;
        let success = false;

        // Method 1: Try messages endpoint
        try {
          data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/messages`, 'POST', {
            text: content,
            type: 'POST'
          });
          success = true;
        } catch (messagesError) {
          console.log('Messages endpoint failed, trying posts endpoint...');

          // Method 2: Try posts endpoint
          try {
            data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/posts`, 'POST', {
              text: content
            });
            success = true;
          } catch (postsError) {
            console.log('Posts endpoint failed, trying generic post...');

            // Method 3: Try generic post endpoint
            data = await makeAPICall(`/api/v1/posts`, 'POST', {
              account_id: currentAccountId,
              text: content,
              provider: 'LINKEDIN'
            });
            success = true;
          }
        }

        if (success) {
          console.log('Post Response:', data);

          container.innerHTML = `
            <div class="account-info">
              <h4>✅ Content Posted Successfully!</h4>
              <p><strong>Posted Content:</strong></p>
              <div style="background: #f5f5f5; padding: 10px; border-radius: 5px; margin: 10px 0;">
                "${content}"
              </div>
              <p><strong>Post ID:</strong> ${data.id || 'Generated'}</p>
              <p><strong>Status:</strong> ${data.status || 'Sent'}</p>
              <p><strong>Posted At:</strong> ${new Date().toLocaleString()}</p>

              <details style="margin-top: 10px;">
                <summary>Full Response Data</summary>
                <pre style="font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
              </details>
            </div>
          `;

          showMessage('Content posted to LinkedIn successfully!', 'success');
          document.getElementById('postContent').value = '';
        }

      } catch (error) {
        console.error('Post Error:', error);

        let errorMessage = error.message;
        let suggestions = '';

        if (error.message.includes('404')) {
          errorMessage = 'Account not found or not properly authenticated';
          suggestions = 'Please check account status and re-authenticate if needed.';
        } else if (error.message.includes('403')) {
          errorMessage = 'Permission denied - account may not have posting permissions';
          suggestions = 'Verify account is fully authenticated and has LinkedIn posting access.';
        } else if (error.message.includes('401')) {
          errorMessage = 'Authentication failed';
          suggestions = 'Please re-authenticate your LinkedIn account.';
        }

        container.innerHTML = `
          <div class="error">
            <h4>❌ Failed to Post Content</h4>
            <p><strong>Error:</strong> ${errorMessage}</p>
            ${suggestions ? `<p><strong>Suggestion:</strong> ${suggestions}</p>` : ''}
            <p><strong>Content attempted:</strong> "${content}"</p>
          </div>
        `;

        showMessage(`Failed to post content: ${errorMessage}`, 'error');
      }
    }

    async function getProfile() {
      const container = document.getElementById('profileContainer');

      try {
        if (!currentAccountId) {
          throw new Error('No account ID available. Please authenticate first.');
        }

        container.innerHTML = '<p>Retrieving LinkedIn profile information...</p>';

        // Try different profile endpoints
        let data;
        let success = false;

        // Method 1: Try account profile endpoint
        try {
          data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/profile`);
          success = true;
        } catch (profileError) {
          console.log('Profile endpoint failed, trying user profile...');

          // Method 2: Try user profile endpoint
          try {
            data = await makeAPICall(`/api/v1/users/profile`, 'GET', null, {
              'X-Account-ID': currentAccountId
            });
            success = true;
          } catch (userError) {
            console.log('User profile failed, trying owner profile...');

            // Method 3: Try owner profile endpoint
            data = await makeAPICall(`/api/v1/users/me`, 'GET', null, {
              'X-Account-ID': currentAccountId
            });
            success = true;
          }
        }

        if (success) {
          console.log('Profile Response:', data);

          // Format profile data nicely
          let profileHtml = `
            <div class="account-info">
              <h4>✅ LinkedIn Profile Information</h4>
          `;

          if (data.name || data.firstName || data.lastName) {
            const fullName = data.name || `${data.firstName || ''} ${data.lastName || ''}`.trim();
            profileHtml += `<p><strong>Name:</strong> ${fullName}</p>`;
          }

          if (data.email) {
            profileHtml += `<p><strong>Email:</strong> ${data.email}</p>`;
          }

          if (data.headline) {
            profileHtml += `<p><strong>Headline:</strong> ${data.headline}</p>`;
          }

          if (data.location) {
            profileHtml += `<p><strong>Location:</strong> ${data.location}</p>`;
          }

          if (data.industry) {
            profileHtml += `<p><strong>Industry:</strong> ${data.industry}</p>`;
          }

          if (data.connections || data.connectionCount) {
            profileHtml += `<p><strong>Connections:</strong> ${data.connections || data.connectionCount}</p>`;
          }

          if (data.profileUrl || data.publicProfileUrl) {
            const profileUrl = data.profileUrl || data.publicProfileUrl;
            profileHtml += `<p><strong>Profile URL:</strong> <a href="${profileUrl}" target="_blank">${profileUrl}</a></p>`;
          }

          profileHtml += `
              <p><strong>Retrieved At:</strong> ${new Date().toLocaleString()}</p>

              <details style="margin-top: 10px;">
                <summary>Full Profile Data</summary>
                <pre style="font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
              </details>
            </div>
          `;

          container.innerHTML = profileHtml;
          showMessage('Profile information retrieved successfully!', 'success');
        }

      } catch (error) {
        console.error('Profile Error:', error);

        let errorMessage = error.message;
        let suggestions = '';

        if (error.message.includes('404')) {
          errorMessage = 'Profile not found or account not authenticated';
          suggestions = 'Please check account status and ensure LinkedIn account is fully connected.';
        } else if (error.message.includes('403')) {
          errorMessage = 'Permission denied - cannot access profile information';
          suggestions = 'Account may not have profile access permissions.';
        } else if (error.message.includes('401')) {
          errorMessage = 'Authentication failed';
          suggestions = 'Please re-authenticate your LinkedIn account.';
        }

        container.innerHTML = `
          <div class="error">
            <h4>❌ Failed to Get Profile</h4>
            <p><strong>Error:</strong> ${errorMessage}</p>
            ${suggestions ? `<p><strong>Suggestion:</strong> ${suggestions}</p>` : ''}
          </div>
        `;

        showMessage(`Failed to get profile: ${errorMessage}`, 'error');
      }
    }

    function showMessage(text, type) {
      const messageDiv = document.getElementById('message');
      messageDiv.textContent = text;
      messageDiv.className = type;
      
      // Auto-clear message after 5 seconds
      setTimeout(() => {
        messageDiv.textContent = '';
        messageDiv.className = '';
      }, 5000);
    }

    // Auto-focus on API key input
    document.addEventListener('DOMContentLoaded', function() {
      // API key is pre-filled, so focus on the OAuth button instead
      document.getElementById('oauthBtn').focus();
    });
  </script>
</body>
</html>
