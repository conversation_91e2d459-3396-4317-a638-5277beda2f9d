#!/usr/bin/env python3
"""
Unified Social Media Messaging System

This module provides a centralized interface for sending messages across all
6 social media platforms: WhatsApp, Telegram, Facebook, Instagram, TikTok, and LinkedIn.
It handles lead management, campaign orchestration, and cross-platform analytics.
"""

import os
import sys
import json
import logging
import asyncio
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# Import all platform APIs
from whatsapp_integration.whatsapp_api import WhatsAppMessagingAPI
from telegram_integration.telegram_api import TelegramMessagingAPI
from facebook_integration.facebook_api import FacebookMessagingAP<PERSON>
from instagram_integration.instagram_api import InstagramMessagingAPI
from tiktok_integration.tiktok_api import TikTokMessagingAPI
from linkedin_integration.linkedin_api import LinkedInMessagingAPI

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class UnifiedMessagingSystem:
    """
    Centralized messaging system for all social media platforms.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize unified messaging system.
        
        Args:
            config_path: Path to unified configuration file
        """
        self.config_path = config_path or 'unified_config.json'
        self.config = self._load_config()
        
        # Initialize platform APIs
        self.platforms = {}
        self._initialize_platforms()
        
        # Campaign tracking
        self.campaign_stats = {
            'total_messages_sent': 0,
            'successful_sends': 0,
            'failed_sends': 0,
            'platforms_used': set(),
            'campaigns': []
        }
    
    def _load_config(self) -> Dict[str, Any]:
        """Load unified configuration."""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
        
        # Return default config
        return {
            'enabled_platforms': ['whatsapp', 'telegram', 'facebook', 'instagram', 'tiktok', 'linkedin'],
            'default_delays': {
                'whatsapp': 5,
                'telegram': 3,
                'facebook': 10,
                'instagram': 15,
                'tiktok': 8,
                'linkedin': 12
            },
            'campaign_settings': {
                'max_concurrent_platforms': 3,
                'retry_failed_messages': True,
                'log_all_activities': True
            }
        }
    
    def _initialize_platforms(self):
        """Initialize all available platform APIs."""
        platform_classes = {
            'whatsapp': WhatsAppMessagingAPI,
            'telegram': TelegramMessagingAPI,
            'facebook': FacebookMessagingAPI,
            'instagram': InstagramMessagingAPI,
            'tiktok': TikTokMessagingAPI,
            'linkedin': LinkedInMessagingAPI
        }
        
        enabled_platforms = self.config.get('enabled_platforms', [])
        
        for platform_name, platform_class in platform_classes.items():
            if platform_name in enabled_platforms:
                try:
                    self.platforms[platform_name] = platform_class()
                    logger.info(f"✅ {platform_name.title()} API initialized")
                except Exception as e:
                    logger.error(f"❌ Failed to initialize {platform_name}: {e}")
    
    def send_message_to_lead(self, lead: Dict[str, Any], message: str, platforms: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Send a message to a lead across specified platforms.
        
        Args:
            lead: Lead data dictionary with platform-specific identifiers
            message: Message text to send
            platforms: List of platforms to use (defaults to all available)
            
        Returns:
            Dict containing results for each platform
        """
        if platforms is None:
            platforms = list(self.platforms.keys())
        
        results = {}
        
        for platform in platforms:
            if platform not in self.platforms:
                results[platform] = {'error': f'Platform {platform} not available'}
                continue
            
            try:
                # Get platform-specific identifier
                recipient = self._get_platform_identifier(lead, platform)
                if not recipient:
                    results[platform] = {'error': f'No {platform} identifier found for lead'}
                    continue
                
                # Send message using appropriate platform API
                api = self.platforms[platform]
                
                if platform == 'whatsapp':
                    result = api.send_message_to_lead(recipient, message, lead)
                elif platform == 'telegram':
                    result = api.send_message_to_lead(recipient, message, lead)
                elif platform == 'facebook':
                    result = api.send_message_to_lead(recipient, message, lead)
                elif platform == 'instagram':
                    result = api.send_direct_message_to_lead(recipient, message, lead)
                elif platform == 'tiktok':
                    result = api.send_message_to_lead(recipient, message, lead)
                elif platform == 'linkedin':
                    # LinkedIn doesn't support direct messaging, so we skip it
                    result = {'info': 'LinkedIn does not support direct messaging via API'}
                else:
                    result = {'error': f'Platform {platform} not implemented'}
                
                results[platform] = result
                self.campaign_stats['successful_sends'] += 1
                self.campaign_stats['platforms_used'].add(platform)
                
            except Exception as e:
                error_msg = f"Failed to send via {platform}: {str(e)}"
                results[platform] = {'error': error_msg}
                self.campaign_stats['failed_sends'] += 1
                logger.error(error_msg)
        
        self.campaign_stats['total_messages_sent'] += 1
        return results
    
    def send_bulk_campaign(self, leads: List[Dict], message_template: str, 
                          platforms: Optional[List[str]] = None, 
                          concurrent: bool = False) -> Dict[str, Any]:
        """
        Send a message campaign to multiple leads across platforms.
        
        Args:
            leads: List of lead dictionaries
            message_template: Message template with placeholders
            platforms: Platforms to use for the campaign
            concurrent: Whether to send to platforms concurrently
            
        Returns:
            Dict containing campaign results
        """
        campaign_id = f"campaign_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        campaign_results = {
            'campaign_id': campaign_id,
            'total_leads': len(leads),
            'platforms': platforms or list(self.platforms.keys()),
            'results': [],
            'summary': {
                'successful_leads': 0,
                'failed_leads': 0,
                'total_messages': 0,
                'platform_stats': {}
            }
        }
        
        logger.info(f"🚀 Starting campaign {campaign_id} for {len(leads)} leads")
        
        for i, lead in enumerate(leads):
            logger.info(f"📤 Processing lead {i+1}/{len(leads)}: {lead.get('name', 'Unknown')}")
            
            try:
                # Personalize message for this lead
                personalized_message = self._personalize_message(message_template, lead)
                
                # Send to platforms
                if concurrent:
                    lead_results = self._send_concurrent(lead, personalized_message, platforms)
                else:
                    lead_results = self.send_message_to_lead(lead, personalized_message, platforms)
                
                # Track results
                lead_success = any('error' not in result for result in lead_results.values())
                if lead_success:
                    campaign_results['summary']['successful_leads'] += 1
                else:
                    campaign_results['summary']['failed_leads'] += 1
                
                campaign_results['results'].append({
                    'lead': lead,
                    'results': lead_results,
                    'success': lead_success
                })
                
                # Update platform stats
                for platform, result in lead_results.items():
                    if platform not in campaign_results['summary']['platform_stats']:
                        campaign_results['summary']['platform_stats'][platform] = {
                            'sent': 0, 'failed': 0
                        }
                    
                    if 'error' in result:
                        campaign_results['summary']['platform_stats'][platform]['failed'] += 1
                    else:
                        campaign_results['summary']['platform_stats'][platform]['sent'] += 1
                
                campaign_results['summary']['total_messages'] += len(lead_results)
                
                # Rate limiting between leads
                if i < len(leads) - 1:
                    import time
                    time.sleep(2)  # 2 second delay between leads
                    
            except Exception as e:
                logger.error(f"Failed to process lead {i}: {e}")
                campaign_results['summary']['failed_leads'] += 1
        
        # Save campaign results
        self._save_campaign_results(campaign_results)
        
        logger.info(f"✅ Campaign {campaign_id} completed: {campaign_results['summary']['successful_leads']} successful, {campaign_results['summary']['failed_leads']} failed")
        return campaign_results
    
    def _send_concurrent(self, lead: Dict, message: str, platforms: Optional[List[str]]) -> Dict[str, Any]:
        """Send message to multiple platforms concurrently."""
        if platforms is None:
            platforms = list(self.platforms.keys())
        
        results = {}
        max_workers = self.config.get('campaign_settings', {}).get('max_concurrent_platforms', 3)
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit tasks for each platform
            future_to_platform = {}
            for platform in platforms:
                if platform in self.platforms:
                    future = executor.submit(self._send_single_platform, lead, message, platform)
                    future_to_platform[future] = platform
            
            # Collect results
            for future in as_completed(future_to_platform):
                platform = future_to_platform[future]
                try:
                    results[platform] = future.result()
                except Exception as e:
                    results[platform] = {'error': str(e)}
        
        return results
    
    def _send_single_platform(self, lead: Dict, message: str, platform: str) -> Dict[str, Any]:
        """Send message via a single platform."""
        recipient = self._get_platform_identifier(lead, platform)
        if not recipient:
            return {'error': f'No {platform} identifier found'}
        
        api = self.platforms[platform]
        
        if platform == 'whatsapp':
            return api.send_message_to_lead(recipient, message, lead)
        elif platform == 'telegram':
            return api.send_message_to_lead(recipient, message, lead)
        elif platform == 'facebook':
            return api.send_message_to_lead(recipient, message, lead)
        elif platform == 'instagram':
            return api.send_direct_message_to_lead(recipient, message, lead)
        else:
            return {'error': f'Platform {platform} not implemented'}
    
    def _get_platform_identifier(self, lead: Dict, platform: str) -> Optional[str]:
        """Get the appropriate identifier for a lead on a specific platform."""
        identifier_map = {
            'whatsapp': ['phone', 'whatsapp_phone'],
            'telegram': ['telegram_username', 'username', 'phone'],
            'facebook': ['facebook_username', 'username'],
            'instagram': ['instagram_username', 'username'],
            'tiktok': ['tiktok_username', 'username'],
            'linkedin': ['linkedin_username', 'email']
        }
        
        possible_fields = identifier_map.get(platform, ['username'])
        
        for field in possible_fields:
            if field in lead and lead[field]:
                return lead[field]
        
        return None
    
    def _personalize_message(self, template: str, lead_data: Dict) -> str:
        """Personalize message template with lead data."""
        try:
            return template.format(**lead_data)
        except KeyError as e:
            logger.warning(f"Missing placeholder data: {e}")
            return template
    
    def _save_campaign_results(self, results: Dict):
        """Save campaign results to file."""
        try:
            filename = f"campaign_results_{results['campaign_id']}.json"
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            logger.info(f"Campaign results saved to {filename}")
        except Exception as e:
            logger.error(f"Failed to save campaign results: {e}")
    
    def get_platform_status(self) -> Dict[str, Any]:
        """Get status of all platforms."""
        status = {}
        
        for platform_name, api in self.platforms.items():
            try:
                platform_stats = api.get_messaging_stats()
                status[platform_name] = platform_stats
            except Exception as e:
                status[platform_name] = {'error': str(e)}
        
        return status
    
    def get_campaign_stats(self) -> Dict[str, Any]:
        """Get overall campaign statistics."""
        return {
            'unified_stats': self.campaign_stats,
            'platform_status': self.get_platform_status()
        }


def main():
    """Command line interface for unified messaging."""
    if len(sys.argv) < 2:
        print("Unified Social Media Messaging System")
        print("Usage:")
        print("  python unified_messaging.py status")
        print("  python unified_messaging.py send_single <lead_json> <message>")
        print("  python unified_messaging.py campaign <leads_file> <message_template>")
        print("  python unified_messaging.py stats")
        sys.exit(1)
    
    command = sys.argv[1]
    system = UnifiedMessagingSystem()
    
    try:
        if command == "status":
            status = system.get_platform_status()
            print(f"Platform Status: {json.dumps(status, indent=2)}")
            
        elif command == "send_single":
            if len(sys.argv) < 4:
                print("Error: send_single requires <lead_json> and <message>")
                sys.exit(1)
            
            lead = json.loads(sys.argv[2])
            message = sys.argv[3]
            results = system.send_message_to_lead(lead, message)
            print(f"Results: {json.dumps(results, indent=2)}")
            
        elif command == "campaign":
            if len(sys.argv) < 4:
                print("Error: campaign requires <leads_file> and <message_template>")
                sys.exit(1)
            
            leads_file = sys.argv[2]
            message_template = sys.argv[3]
            
            with open(leads_file, 'r') as f:
                leads = json.load(f)
            
            results = system.send_bulk_campaign(leads, message_template)
            print(f"Campaign Results: {json.dumps(results, indent=2)}")
            
        elif command == "stats":
            stats = system.get_campaign_stats()
            print(f"Campaign Stats: {json.dumps(stats, indent=2)}")
            
        else:
            print(f"Unknown command: {command}")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
