<!DOCTYPE html>
<html>
<head>
  <title>Connect Telegram with Unipile</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 50px auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 20px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .telegram-logo {
      color: #0088cc;
      font-size: 32px;
      font-weight: bold;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    button {
      background: linear-gradient(135deg, #0088cc 0%, #005580 100%);
      color: white;
      border: none;
      padding: 14px 28px;
      font-size: 16px;
      border-radius: 30px;
      cursor: pointer;
      margin: 10px 5px;
      min-width: 160px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0,136,204,0.3);
    }
    button:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(0,136,204,0.4);
    }
    button:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    .error {
      color: #e74c3c;
      margin-top: 10px;
      padding: 15px;
      background-color: #fdf2f2;
      border-radius: 12px;
      border-left: 5px solid #e74c3c;
    }
    .success {
      color: #27ae60;
      margin-top: 10px;
      padding: 15px;
      background-color: #f0f9f0;
      border-radius: 12px;
      border-left: 5px solid #27ae60;
    }
    .warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
      padding: 20px;
      border-radius: 15px;
      margin-bottom: 25px;
      box-shadow: 0 4px 15px rgba(243,156,18,0.3);
    }
    .config-section {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 20px;
      border-radius: 15px;
      margin-bottom: 25px;
      border: 2px solid #dee2e6;
    }
    input[type="text"], input[type="tel"], textarea {
      width: 100%;
      padding: 15px;
      margin: 10px 0;
      border: 2px solid #e1e8ed;
      border-radius: 12px;
      box-sizing: border-box;
      transition: all 0.3s ease;
      font-size: 16px;
    }
    input[type="text"]:focus, input[type="tel"]:focus, textarea:focus {
      border-color: #0088cc;
      outline: none;
      box-shadow: 0 0 0 3px rgba(0,136,204,0.1);
    }
    textarea {
      height: 120px;
      resize: vertical;
    }
    .section {
      margin-bottom: 30px;
      padding: 25px;
      border: 2px solid #e0e6ed;
      border-radius: 20px;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    }
    .section h3 {
      margin-top: 0;
      color: #0088cc;
      font-size: 24px;
    }
    .account-info {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 25px;
      border-radius: 15px;
      margin: 20px 0;
      box-shadow: 0 6px 25px rgba(102,126,234,0.3);
    }
    .button-group {
      text-align: center;
      margin: 25px 0;
    }
    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin: 25px 0;
    }
    .feature-card {
      background: white;
      padding: 25px;
      border-radius: 15px;
      border: 2px solid #e1e8ed;
      text-align: center;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    }
    .feature-card:hover {
      border-color: #0088cc;
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0,136,204,0.15);
    }
    .step-indicator {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      text-align: center;
    }
    .phone-input {
      font-family: monospace;
      font-size: 18px;
      text-align: center;
    }
    .verification-code {
      font-family: monospace;
      font-size: 24px;
      text-align: center;
      letter-spacing: 3px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1><span class="telegram-logo">📱 Telegram</span> Integration with Unipile</h1>
      <p>Connect your Telegram account for automated messaging, channel management, and bot interactions</p>
    </div>
    
    <div class="warning">
      <strong>⚠️ Security Notice:</strong> Never expose your API key in client-side code. 
      This is for development/testing only. In production, use a backend server to handle API calls.
    </div>

    <div class="config-section">
      <h3>🔧 Configuration</h3>
      <label for="apiKey">API Key:</label>
      <input type="text" id="apiKey" value="zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=" placeholder="Enter your Unipile API key" />
      
      <label for="dsn">DSN:</label>
      <input type="text" id="dsn" value="https://api1.unipile.com:13115" placeholder="Enter your DSN" />
    </div>

    <!-- Authentication Section -->
    <div class="section">
      <h3>1. 📱 Connect Telegram Account</h3>
      <p>Telegram uses QR code authentication. Generate a QR code and scan it with your Telegram mobile app.</p>

      <div class="step-indicator">
        <strong>Step 1:</strong> Generate QR code for Telegram authentication
      </div>

      <div style="margin-bottom: 20px;">
        <div class="button-group">
          <button onclick="generateQRCode()">📱 Generate Telegram QR Code</button>
        </div>
      </div>

      <div id="qrContainer"></div>
      <div id="authContainer"></div>
    </div>

    <!-- Account Status Section -->
    <div class="section">
      <h3>2. 📊 Check Account Status</h3>
      <p>Verify your Telegram account connection and status.</p>
      <div class="button-group">
        <button onclick="checkAccountStatus()">📋 Check Status</button>
      </div>
      <div id="statusContainer"></div>
    </div>

    <!-- Features Grid -->
    <div class="section">
      <h3>3. 🚀 Telegram Features</h3>
      <div class="feature-grid">
        
        <!-- Direct Messages -->
        <div class="feature-card">
          <h4>💬 Send Messages</h4>
          <label for="messageRecipient">To (username or phone):</label>
          <input type="text" id="messageRecipient" placeholder="@username or +**********" />
          <label for="messageText">Message:</label>
          <textarea id="messageText" placeholder="Enter your message...

Hello from Telegram automation! 🤖"></textarea>
          <button onclick="sendMessage()">📤 Send Message</button>
          <div id="messageContainer"></div>
        </div>

        <!-- Channel Messages -->
        <div class="feature-card">
          <h4>📢 Channel Broadcasting</h4>
          <label for="channelName">Channel:</label>
          <input type="text" id="channelName" placeholder="@mychannel" />
          <label for="channelMessage">Message:</label>
          <textarea id="channelMessage" placeholder="Enter channel message...

📢 Important announcement!
🚀 New features available now!"></textarea>
          <button onclick="sendChannelMessage()">📡 Broadcast</button>
          <div id="channelContainer"></div>
        </div>

        <!-- Profile Info -->
        <div class="feature-card">
          <h4>👤 Profile Info</h4>
          <p>Get your Telegram profile information and account details.</p>
          <button onclick="getProfile()">📊 Get Profile</button>
          <div id="profileContainer"></div>
        </div>

        <!-- Chats -->
        <div class="feature-card">
          <h4>💭 Chat Management</h4>
          <p>Retrieve your chats, groups, and channel list.</p>
          <button onclick="getChats()">📝 Get Chats</button>
          <div id="chatsContainer"></div>
        </div>

      </div>
    </div>

    <div id="message"></div>
  </div>

  <script>
    let currentAccountId = null;

    async function makeAPICall(endpoint, method = 'GET', body = null, additionalHeaders = {}) {
      const apiKey = document.getElementById('apiKey').value.trim();
      const dsn = document.getElementById('dsn').value.trim();
      
      if (!apiKey || !dsn) {
        throw new Error('Please enter both API key and DSN');
      }

      const headers = {
        'X-API-KEY': apiKey,
        'accept': 'application/json',
        ...additionalHeaders
      };

      if (method !== 'GET') {
        headers['Content-Type'] = 'application/json';
      }

      const config = {
        method: method,
        headers: headers
      };

      if (body) {
        config.body = JSON.stringify(body);
      }

      const response = await fetch(`${dsn}${endpoint}`, config);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return await response.json();
    }

    async function generateQRCode() {
      const qrContainer = document.getElementById('qrContainer');
      const authContainer = document.getElementById('authContainer');

      try {
        qrContainer.innerHTML = '<p>📱 Generating Telegram QR code...</p>';
        authContainer.innerHTML = '';

        const data = await makeAPICall('/api/v1/accounts', 'POST', {
          provider: 'TELEGRAM'
        });

        console.log('QR Response:', data);
        currentAccountId = data.account_id;

        // Handle different response structures for QR code
        let qrCodeString = null;

        if (data.qrcode) {
          qrCodeString = data.qrcode;
        } else if (data.checkpoint && data.checkpoint.qrcode) {
          qrCodeString = data.checkpoint.qrcode;
        } else if (data.qr_code) {
          qrCodeString = data.qr_code;
        }

        if (qrCodeString) {
          const qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(qrCodeString)}`;

          qrContainer.innerHTML = `
            <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px; color: white; margin: 20px 0;">
              <h4>📱 Telegram QR Code</h4>
              <img src="${qrImageUrl}" alt="Scan with Telegram" style="border-radius: 10px; margin: 10px 0;" />
              <p><strong>Instructions:</strong></p>
              <ol style="text-align: left; max-width: 300px; margin: 0 auto;">
                <li>Open Telegram mobile app</li>
                <li>Go to Settings → Devices</li>
                <li>Tap "Link Desktop Device"</li>
                <li>Scan this QR code</li>
              </ol>
              <p><strong>Account ID:</strong> ${currentAccountId}</p>
            </div>
          `;

          authContainer.innerHTML = `
            <div class="step-indicator">
              <strong>Step 2:</strong> Scan the QR code with Telegram mobile app, then check your account status
            </div>
          `;

          showMessage('Telegram QR code generated! Scan with your mobile app.', 'success');
        } else if (data.object === 'Account') {
          qrContainer.innerHTML = '';
          authContainer.innerHTML = `
            <div class="account-info">
              <h4>✅ Telegram Already Connected!</h4>
              <p><strong>Account ID:</strong> ${currentAccountId}</p>
              <p><strong>Status:</strong> Already authenticated</p>
              <p>🚀 You can now use all Telegram features!</p>
            </div>
          `;
          showMessage('Telegram account already connected!', 'success');
        } else {
          console.error('Full API Response:', JSON.stringify(data, null, 2));
          throw new Error('No QR code received from API');
        }

      } catch (error) {
        console.error('QR Generation Error:', error);
        showMessage(`Failed to generate QR code: ${error.message}`, 'error');
        qrContainer.innerHTML = '';
        authContainer.innerHTML = `
          <div class="error">
            <h4>❌ QR Code Generation Failed</h4>
            <p>${error.message}</p>
          </div>
        `;
      }
    }



    async function checkAccountStatus() {
      const container = document.getElementById('statusContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('No account ID available. Please authenticate first.');
        }

        container.innerHTML = '<p>🔄 Checking account status...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}`);
        console.log('Status Response:', data);

        if (data.object === 'Account') {
          const sources = data.sources || [];
          const status = sources.length > 0 ? sources[0].status : 'Unknown';
          
          container.innerHTML = `
            <div class="account-info">
              <h4>✅ Account Status: ${status}</h4>
              <p><strong>Type:</strong> ${data.type}</p>
              <p><strong>Name:</strong> ${data.name}</p>
              <p><strong>ID:</strong> ${data.id}</p>
              <p><strong>Created:</strong> ${new Date(data.created_at).toLocaleString()}</p>
              <p><strong>Sources:</strong> ${sources.length} available</p>
            </div>
          `;
          showMessage(`Account status: ${status}`, status === 'OK' ? 'success' : 'error');
        } else {
          container.innerHTML = `
            <div class="account-info">
              <h4>Account Information</h4>
              <pre>${JSON.stringify(data, null, 2)}</pre>
            </div>
          `;
        }

      } catch (error) {
        console.error('Status Error:', error);
        if (error.message.includes('404')) {
          container.innerHTML = `
            <div class="error">
              <h4>❌ Account Not Found</h4>
              <p>Please authenticate again.</p>
            </div>
          `;
        } else {
          container.innerHTML = `
            <div class="error">
              <h4>❌ Error Checking Status</h4>
              <p>${error.message}</p>
            </div>
          `;
        }
        showMessage(`Failed to check status: ${error.message}`, 'error');
      }
    }

    async function sendMessage() {
      const container = document.getElementById('messageContainer');
      const recipient = document.getElementById('messageRecipient').value.trim();
      const message = document.getElementById('messageText').value.trim();
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }
        if (!recipient || !message) {
          throw new Error('Please enter both recipient and message.');
        }

        container.innerHTML = '<p>📤 Sending message...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/messages`, 'POST', {
          to: recipient,
          text: message
        });
        
        container.innerHTML = `
          <div class="success">
            <h5>✅ Message Sent!</h5>
            <p><strong>To:</strong> ${recipient}</p>
            <p><strong>Message:</strong> "${message}"</p>
          </div>
        `;
        
        showMessage('Message sent successfully!', 'success');
        document.getElementById('messageText').value = '';

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Send</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to send message: ${error.message}`, 'error');
      }
    }

    async function sendChannelMessage() {
      const container = document.getElementById('channelContainer');
      const channel = document.getElementById('channelName').value.trim();
      const message = document.getElementById('channelMessage').value.trim();
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }
        if (!channel || !message) {
          throw new Error('Please enter both channel and message.');
        }

        container.innerHTML = '<p>📡 Broadcasting to channel...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/channels/${encodeURIComponent(channel)}/messages`, 'POST', {
          text: message
        });
        
        container.innerHTML = `
          <div class="success">
            <h5>✅ Broadcast Sent!</h5>
            <p><strong>Channel:</strong> ${channel}</p>
            <p><strong>Message:</strong> "${message}"</p>
          </div>
        `;
        
        showMessage('Channel message sent successfully!', 'success');
        document.getElementById('channelMessage').value = '';

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Broadcast</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to send channel message: ${error.message}`, 'error');
      }
    }

    async function getProfile() {
      const container = document.getElementById('profileContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }

        container.innerHTML = '<p>👤 Getting profile info...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/profile`);
        
        container.innerHTML = `
          <div class="success">
            <h5>✅ Profile Retrieved</h5>
            <p><strong>Username:</strong> ${data.username || 'N/A'}</p>
            <p><strong>First Name:</strong> ${data.first_name || 'N/A'}</p>
            <p><strong>Last Name:</strong> ${data.last_name || 'N/A'}</p>
            <p><strong>Phone:</strong> ${data.phone_number || 'N/A'}</p>
          </div>
        `;
        
        showMessage('Profile information retrieved!', 'success');

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Get Profile</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to get profile: ${error.message}`, 'error');
      }
    }

    async function getChats() {
      const container = document.getElementById('chatsContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }

        container.innerHTML = '<p>💭 Getting chats...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/chats`);
        
        const chats = data.chats || data.items || [];
        container.innerHTML = `
          <div class="success">
            <h5>✅ Chats Retrieved</h5>
            <p><strong>Count:</strong> ${chats.length}</p>
            ${chats.length > 0 ? `<p>Recent chats loaded</p>` : '<p>No chats data available</p>'}
          </div>
        `;
        
        showMessage('Chats information retrieved!', 'success');

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Get Chats</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to get chats: ${error.message}`, 'error');
      }
    }

    function showMessage(text, type) {
      const messageDiv = document.getElementById('message');
      messageDiv.textContent = text;
      messageDiv.className = type;
      
      // Auto-clear message after 5 seconds
      setTimeout(() => {
        messageDiv.textContent = '';
        messageDiv.className = '';
      }, 5000);
    }

    // Auto-focus on generate QR button
    document.addEventListener('DOMContentLoaded', function() {
      // Focus is handled automatically by the interface
    });
  </script>
</body>
</html>
