<!DOCTYPE html>
<html>
<head>
  <title>Connect TikTok with Unipile</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 50px auto;
      padding: 20px;
      background: linear-gradient(135deg, #ff0050 0%, #ff4081 25%, #e91e63 50%, #ad1457 75%, #880e4f 100%);
      min-height: 100vh;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 25px;
      box-shadow: 0 10px 40px rgba(0,0,0,0.2);
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .tiktok-logo {
      background: linear-gradient(135deg, #ff0050 0%, #ff4081 50%, #e91e63 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-size: 36px;
      font-weight: bold;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    button {
      background: linear-gradient(135deg, #ff0050 0%, #ff4081 50%, #e91e63 100%);
      color: white;
      border: none;
      padding: 16px 32px;
      font-size: 16px;
      border-radius: 35px;
      cursor: pointer;
      margin: 12px 6px;
      min-width: 170px;
      transition: all 0.3s ease;
      box-shadow: 0 6px 20px rgba(255,0,80,0.3);
      font-weight: bold;
    }
    button:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(255,0,80,0.4);
    }
    button:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    .error {
      color: #e74c3c;
      margin-top: 10px;
      padding: 15px;
      background-color: #fdf2f2;
      border-radius: 15px;
      border-left: 5px solid #e74c3c;
    }
    .success {
      color: #27ae60;
      margin-top: 10px;
      padding: 15px;
      background-color: #f0f9f0;
      border-radius: 15px;
      border-left: 5px solid #27ae60;
    }
    .warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
      padding: 20px;
      border-radius: 15px;
      margin-bottom: 25px;
      box-shadow: 0 4px 15px rgba(243,156,18,0.3);
    }
    .config-section {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 20px;
      border-radius: 15px;
      margin-bottom: 25px;
      border: 2px solid #dee2e6;
    }
    input[type="text"], input[type="password"], input[type="url"], textarea {
      width: 100%;
      padding: 15px;
      margin: 10px 0;
      border: 2px solid #e1e8ed;
      border-radius: 15px;
      box-sizing: border-box;
      transition: all 0.3s ease;
      font-size: 16px;
    }
    input[type="text"]:focus, input[type="password"]:focus, input[type="url"]:focus, textarea:focus {
      border-color: #ff0050;
      outline: none;
      box-shadow: 0 0 0 3px rgba(255,0,80,0.1);
    }
    textarea {
      height: 120px;
      resize: vertical;
    }
    .section {
      margin-bottom: 30px;
      padding: 25px;
      border: 2px solid #e0e6ed;
      border-radius: 25px;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      box-shadow: 0 6px 20px rgba(0,0,0,0.08);
    }
    .section h3 {
      margin-top: 0;
      color: #ff0050;
      font-size: 26px;
      font-weight: bold;
    }
    .account-info {
      background: linear-gradient(135deg, #ff0050 0%, #ff4081 50%, #e91e63 100%);
      color: white;
      padding: 25px;
      border-radius: 20px;
      margin: 20px 0;
      box-shadow: 0 8px 30px rgba(255,0,80,0.3);
    }
    .button-group {
      text-align: center;
      margin: 25px 0;
    }
    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 25px;
      margin: 25px 0;
    }
    .feature-card {
      background: white;
      padding: 25px;
      border-radius: 20px;
      border: 2px solid #e1e8ed;
      text-align: center;
      transition: all 0.3s ease;
      box-shadow: 0 6px 20px rgba(0,0,0,0.08);
    }
    .feature-card:hover {
      border-color: #ff0050;
      transform: translateY(-8px);
      box-shadow: 0 12px 30px rgba(255,0,80,0.2);
    }
    .step-indicator {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
      padding: 15px;
      border-radius: 12px;
      margin: 15px 0;
      text-align: center;
      font-weight: bold;
    }
    .video-preview {
      background: #f8f9fa;
      border: 2px dashed #dee2e6;
      border-radius: 15px;
      padding: 20px;
      margin: 15px 0;
      text-align: center;
      color: #6c757d;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1><span class="tiktok-logo">🎵 TikTok</span> Integration with Unipile</h1>
      <p>Connect your TikTok account for automated video posting, messaging, and content management</p>
    </div>
    
    <div class="warning">
      <strong>⚠️ Security Notice:</strong> Never expose your API key in client-side code. 
      This is for development/testing only. In production, use a backend server to handle API calls.
    </div>

    <div class="config-section">
      <h3>🔧 Configuration</h3>
      <label for="apiKey">API Key:</label>
      <input type="text" id="apiKey" value="zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=" placeholder="Enter your Unipile API key" />
      
      <label for="dsn">DSN:</label>
      <input type="text" id="dsn" value="https://api1.unipile.com:13115" placeholder="Enter your DSN" />
    </div>

    <!-- Authentication Section -->
    <div class="section">
      <h3>1. 🔐 Connect TikTok Account</h3>
      <p>Enter your TikTok credentials to connect your account:</p>
      
      <div style="margin-bottom: 20px;">
        <label for="username">TikTok Username:</label>
        <input type="text" id="username" placeholder="Enter your TikTok username" />
        
        <label for="password">TikTok Password:</label>
        <input type="password" id="password" placeholder="Enter your TikTok password" />
        
        <div class="button-group">
          <button onclick="authenticateWithCredentials()">🔗 Connect TikTok Account</button>
        </div>
      </div>
      
      <div id="authContainer"></div>
    </div>

    <!-- Account Status Section -->
    <div class="section">
      <h3>2. 📊 Check Account Status</h3>
      <p>Verify your TikTok account connection and status.</p>
      <div class="button-group">
        <button onclick="checkAccountStatus()">📋 Check Status</button>
      </div>
      <div id="statusContainer"></div>
    </div>

    <!-- Features Grid -->
    <div class="section">
      <h3>3. 🚀 TikTok Features</h3>
      <div class="feature-grid">
        
        <!-- Post Video -->
        <div class="feature-card">
          <h4>🎬 Post Video</h4>
          <label for="videoUrl">Video URL:</label>
          <input type="url" id="videoUrl" placeholder="https://example.com/video.mp4" />
          <label for="videoCaption">Caption:</label>
          <textarea id="videoCaption" placeholder="Enter your video caption...

🎵 Amazing TikTok content! 
#tiktok #viral #trending #fyp #automation"></textarea>
          <div class="video-preview">
            📹 Video will be posted to your TikTok feed
          </div>
          <button onclick="postVideo()">🎵 Post to TikTok</button>
          <div id="videoContainer"></div>
        </div>

        <!-- Send Messages -->
        <div class="feature-card">
          <h4>💬 Direct Messages</h4>
          <label for="messageRecipient">To (username):</label>
          <input type="text" id="messageRecipient" placeholder="@username" />
          <label for="messageText">Message:</label>
          <textarea id="messageText" placeholder="Enter your message...

Hey! Check out my latest TikTok! 🎵
#tiktok #viral"></textarea>
          <button onclick="sendMessage()">📤 Send Message</button>
          <div id="messageContainer"></div>
        </div>

        <!-- Profile Info -->
        <div class="feature-card">
          <h4>👤 Profile Info</h4>
          <p>Get your TikTok profile information and statistics.</p>
          <button onclick="getProfile()">📊 Get Profile</button>
          <div id="profileContainer"></div>
        </div>

        <!-- Followers -->
        <div class="feature-card">
          <h4>👥 Followers</h4>
          <p>Retrieve your followers list and engagement analytics.</p>
          <button onclick="getFollowers()">📈 Get Followers</button>
          <div id="followersContainer"></div>
        </div>

        <!-- Videos -->
        <div class="feature-card">
          <h4>🎬 My Videos</h4>
          <p>View your posted TikTok videos and performance metrics.</p>
          <button onclick="getVideos()">🎥 Get Videos</button>
          <div id="videosContainer"></div>
        </div>

      </div>
    </div>

    <div id="message"></div>
  </div>

  <script>
    let currentAccountId = null;

    async function makeAPICall(endpoint, method = 'GET', body = null, additionalHeaders = {}) {
      const apiKey = document.getElementById('apiKey').value.trim();
      const dsn = document.getElementById('dsn').value.trim();
      
      if (!apiKey || !dsn) {
        throw new Error('Please enter both API key and DSN');
      }

      const headers = {
        'X-API-KEY': apiKey,
        'accept': 'application/json',
        ...additionalHeaders
      };

      if (method !== 'GET') {
        headers['Content-Type'] = 'application/json';
      }

      const config = {
        method: method,
        headers: headers
      };

      if (body) {
        config.body = JSON.stringify(body);
      }

      const response = await fetch(`${dsn}${endpoint}`, config);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return await response.json();
    }

    async function authenticateWithCredentials() {
      const container = document.getElementById('authContainer');
      const username = document.getElementById('username').value.trim();
      const password = document.getElementById('password').value.trim();
      
      try {
        if (!username || !password) {
          throw new Error('Please enter both username and password');
        }

        container.innerHTML = '<p>🔄 Authenticating with TikTok...</p>';

        const data = await makeAPICall('/api/v1/accounts', 'POST', {
          provider: 'TIKTOK',
          username: username,
          password: password
        });
        
        console.log('Auth Response:', data);
        currentAccountId = data.account_id;

        if (data.object === 'Account') {
          container.innerHTML = `
            <div class="account-info">
              <h4>✅ TikTok Authentication Successful!</h4>
              <p><strong>Account ID:</strong> ${currentAccountId}</p>
              <p><strong>Username:</strong> ${username}</p>
              <p><strong>Status:</strong> Connected</p>
              <details style="margin-top: 10px;">
                <summary>Full Response Data</summary>
                <pre style="font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
              </details>
            </div>
          `;
          showMessage('TikTok account connected successfully!', 'success');
        } else if (data.object === 'Checkpoint') {
          const checkpointType = data.checkpoint?.type || 'Unknown';
          container.innerHTML = `
            <div class="account-info" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
              <h4>⚠️ TikTok Checkpoint Required</h4>
              <p><strong>Type:</strong> ${checkpointType}</p>
              <p><strong>Account ID:</strong> ${data.account_id}</p>
              ${checkpointType === 'CAPTCHA' ? `
                <p>CAPTCHA verification required. Contact Unipile support for assistance.</p>
              ` : checkpointType === '2FA' ? `
                <p>Two-factor authentication required. Check your phone/email for verification code.</p>
              ` : `
                <p>Additional verification required: ${checkpointType}</p>
              `}
            </div>
          `;
          showMessage(`TikTok requires ${checkpointType} verification`, 'error');
        }
        
        // Clear password field for security
        document.getElementById('password').value = '';

      } catch (error) {
        console.error('Auth Error:', error);
        showMessage(`Failed to authenticate: ${error.message}`, 'error');
        container.innerHTML = `
          <div class="error">
            <h4>❌ Authentication Failed</h4>
            <p>${error.message}</p>
          </div>
        `;
      }
    }

    async function checkAccountStatus() {
      const container = document.getElementById('statusContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('No account ID available. Please authenticate first.');
        }

        container.innerHTML = '<p>🔄 Checking account status...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}`);
        console.log('Status Response:', data);

        if (data.object === 'Account') {
          const sources = data.sources || [];
          const status = sources.length > 0 ? sources[0].status : 'Unknown';
          
          container.innerHTML = `
            <div class="account-info">
              <h4>✅ Account Status: ${status}</h4>
              <p><strong>Type:</strong> ${data.type}</p>
              <p><strong>Name:</strong> ${data.name}</p>
              <p><strong>ID:</strong> ${data.id}</p>
              <p><strong>Created:</strong> ${new Date(data.created_at).toLocaleString()}</p>
              <p><strong>Sources:</strong> ${sources.length} available</p>
            </div>
          `;
          showMessage(`Account status: ${status}`, status === 'OK' ? 'success' : 'error');
        } else {
          container.innerHTML = `
            <div class="account-info">
              <h4>Account Information</h4>
              <pre>${JSON.stringify(data, null, 2)}</pre>
            </div>
          `;
        }

      } catch (error) {
        console.error('Status Error:', error);
        if (error.message.includes('404')) {
          container.innerHTML = `
            <div class="error">
              <h4>❌ Account Not Found</h4>
              <p>Please authenticate again.</p>
            </div>
          `;
        } else {
          container.innerHTML = `
            <div class="error">
              <h4>❌ Error Checking Status</h4>
              <p>${error.message}</p>
            </div>
          `;
        }
        showMessage(`Failed to check status: ${error.message}`, 'error');
      }
    }

    async function postVideo() {
      const container = document.getElementById('videoContainer');
      const videoUrl = document.getElementById('videoUrl').value.trim();
      const caption = document.getElementById('videoCaption').value.trim();
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }
        if (!videoUrl) {
          throw new Error('Please enter a video URL.');
        }

        container.innerHTML = '<p>🎬 Posting video to TikTok...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/posts`, 'POST', {
          video_url: videoUrl,
          text: caption || ""
        });
        
        container.innerHTML = `
          <div class="success">
            <h5>✅ Video Posted Successfully!</h5>
            <p><strong>Video URL:</strong> ${videoUrl}</p>
            ${caption ? `<p><strong>Caption:</strong> "${caption}"</p>` : ''}
            <p><strong>Posted At:</strong> ${new Date().toLocaleString()}</p>
          </div>
        `;
        
        showMessage('Video posted to TikTok successfully!', 'success');
        document.getElementById('videoUrl').value = '';
        document.getElementById('videoCaption').value = '';

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Post Video</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to post video: ${error.message}`, 'error');
      }
    }

    async function sendMessage() {
      const container = document.getElementById('messageContainer');
      const recipient = document.getElementById('messageRecipient').value.trim();
      const message = document.getElementById('messageText').value.trim();
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }
        if (!recipient || !message) {
          throw new Error('Please enter both recipient and message.');
        }

        container.innerHTML = '<p>📤 Sending message...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/messages`, 'POST', {
          to: recipient,
          text: message
        });
        
        container.innerHTML = `
          <div class="success">
            <h5>✅ Message Sent!</h5>
            <p><strong>To:</strong> ${recipient}</p>
            <p><strong>Message:</strong> "${message}"</p>
          </div>
        `;
        
        showMessage('Message sent successfully!', 'success');
        document.getElementById('messageText').value = '';

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Send</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to send message: ${error.message}`, 'error');
      }
    }

    async function getProfile() {
      const container = document.getElementById('profileContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }

        container.innerHTML = '<p>👤 Getting profile info...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/profile`);
        
        container.innerHTML = `
          <div class="success">
            <h5>✅ Profile Retrieved</h5>
            <p><strong>Username:</strong> ${data.username || 'N/A'}</p>
            <p><strong>Display Name:</strong> ${data.display_name || 'N/A'}</p>
            <p><strong>Followers:</strong> ${data.followers_count || 'N/A'}</p>
            <p><strong>Following:</strong> ${data.following_count || 'N/A'}</p>
            <p><strong>Videos:</strong> ${data.videos_count || 'N/A'}</p>
            <p><strong>Likes:</strong> ${data.likes_count || 'N/A'}</p>
          </div>
        `;
        
        showMessage('Profile information retrieved!', 'success');

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Get Profile</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to get profile: ${error.message}`, 'error');
      }
    }

    async function getFollowers() {
      const container = document.getElementById('followersContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }

        container.innerHTML = '<p>👥 Getting followers...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/followers`);
        
        const followers = data.followers || data.items || [];
        container.innerHTML = `
          <div class="success">
            <h5>✅ Followers Retrieved</h5>
            <p><strong>Count:</strong> ${followers.length}</p>
            ${followers.length > 0 ? `<p>Recent followers loaded</p>` : '<p>No followers data available</p>'}
          </div>
        `;
        
        showMessage('Followers information retrieved!', 'success');

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Get Followers</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to get followers: ${error.message}`, 'error');
      }
    }

    async function getVideos() {
      const container = document.getElementById('videosContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }

        container.innerHTML = '<p>🎬 Getting videos...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/videos`);
        
        const videos = data.videos || data.items || [];
        container.innerHTML = `
          <div class="success">
            <h5>✅ Videos Retrieved</h5>
            <p><strong>Count:</strong> ${videos.length}</p>
            ${videos.length > 0 ? `<p>Your TikTok videos loaded</p>` : '<p>No videos data available</p>'}
          </div>
        `;
        
        showMessage('Videos information retrieved!', 'success');

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Get Videos</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to get videos: ${error.message}`, 'error');
      }
    }

    function showMessage(text, type) {
      const messageDiv = document.getElementById('message');
      messageDiv.textContent = text;
      messageDiv.className = type;
      
      // Auto-clear message after 5 seconds
      setTimeout(() => {
        messageDiv.textContent = '';
        messageDiv.className = '';
      }, 5000);
    }

    // Auto-focus on username input
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('username').focus();
    });
  </script>
</body>
</html>
