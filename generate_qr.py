#!/usr/bin/env python3
"""
Generate WhatsApp QR code for scanning.
"""

import sys
import os
import json
import time

# Add the integrations directory to the path
sys.path.append('integrations')

from whatsapp_integration.whatsapp_api import WhatsAppMessaging

def load_config():
    """Load API key from config file."""
    try:
        with open('integrations/whatsapp_integration/config.json', 'r') as f:
            config = json.load(f)
            return config.get('api_key')
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return None

def main():
    """Generate QR code and provide instructions."""
    print("📱 WhatsApp QR Code Generator")
    print("=" * 40)
    
    # Load API key
    api_key = load_config()
    if not api_key:
        print("❌ No API key found in config.json")
        return
    
    print(f"✅ API key loaded: {api_key[:10]}...")
    
    try:
        # Initialize WhatsApp
        print("\n🔧 Initializing WhatsApp...")
        whatsapp = WhatsAppMessaging(unipile_api_key=api_key)
        print("✅ WhatsApp initialized!")
        
        # Generate QR code
        print("\n📱 Generating QR code...")
        auth_result = whatsapp.authenticate_account()
        
        account_id = auth_result.get('account_id')
        qr_code = auth_result.get('qr_code')
        
        print(f"✅ QR Code generated successfully!")
        print(f"📱 Account ID: {account_id}")
        print(f"🔗 QR Code String: {qr_code}")
        
        # Create QR code URL for easy viewing
        import urllib.parse
        qr_url = f"https://api.qrserver.com/v1/create-qr-code/?size=300x300&data={urllib.parse.quote(qr_code)}"
        
        print(f"\n🌐 View QR Code in browser:")
        print(f"   {qr_url}")
        
        print(f"\n📋 Instructions:")
        print(f"   1. Open WhatsApp on your phone")
        print(f"   2. Go to Settings → Linked Devices")
        print(f"   3. Tap 'Link a Device'")
        print(f"   4. Scan the QR code from the URL above")
        print(f"   5. Or use the web interface: integrations/whatsapp_integration/whatsapp.html")
        
        # Save account ID for later use
        with open('whatsapp_account.txt', 'w') as f:
            f.write(f"Account ID: {account_id}\n")
            f.write(f"QR Code: {qr_code}\n")
            f.write(f"QR URL: {qr_url}\n")
        
        print(f"\n💾 Account details saved to: whatsapp_account.txt")
        
        # Wait a bit and check status
        print(f"\n⏳ Waiting 30 seconds for you to scan the QR code...")
        time.sleep(30)
        
        try:
            status = whatsapp.unipile.check_account_status()
            print(f"📊 Account Status: {status}")
            
            # If authenticated, offer to send test message
            if status.get('object') == 'Account':
                print(f"\n🎉 Authentication successful!")
                
                test_phone = input(f"📞 Enter your phone number to send test message (or press Enter to skip): ").strip()
                if test_phone:
                    try:
                        result = whatsapp.send_message(test_phone, "🎉 WhatsApp API test successful!")
                        print(f"✅ Test message sent: {result}")
                    except Exception as e:
                        print(f"❌ Failed to send test message: {e}")
            else:
                print(f"⏳ Account not yet authenticated. Please scan the QR code.")
                
        except Exception as e:
            print(f"⏳ Account status check failed (this is normal if not yet scanned): {e}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
