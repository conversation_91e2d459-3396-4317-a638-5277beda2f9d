<!DOCTYPE html>
<html>
<head>
  <title>Connect WhatsApp with Unipile</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 600px;
      margin: 50px auto;
      padding: 20px;
    }
    .container {
      text-align: center;
    }
    button {
      background-color: #25D366;
      color: white;
      border: none;
      padding: 12px 24px;
      font-size: 16px;
      border-radius: 5px;
      cursor: pointer;
    }
    button:hover {
      background-color: #128C7E;
    }
    button:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
    .error {
      color: red;
      margin-top: 10px;
    }
    .success {
      color: green;
      margin-top: 10px;
    }
    .warning {
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      color: #856404;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    #qrContainer {
      margin-top: 20px;
    }
    .config-section {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
      text-align: left;
    }
    input[type="text"] {
      width: 100%;
      padding: 8px;
      margin: 5px 0;
      border: 1px solid #ddd;
      border-radius: 3px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>Connect WhatsApp with Unipile</h2>
    
    <div class="warning">
      <strong>⚠️ Security Notice:</strong> Never expose your API key in client-side code. 
      This is for development/testing only. In production, use a backend server to handle API calls.
    </div>

    <div class="config-section">
      <h3>Configuration</h3>
      <label for="apiKey">API Key:</label>
      <input type="text" id="apiKey" placeholder="Enter your Unipile API key" />
      
      <label for="dsn">DSN:</label>
      <input type="text" id="dsn" value="https://api1.unipile.com:13115" placeholder="Enter your DSN" />
    </div>

    <button id="generateBtn" onclick="generateQRCode()">Generate QR Code</button>
    <div id="qrContainer"></div>
    <div id="message"></div>
  </div>

  <script>
    async function generateQRCode() {
      const apiKey = document.getElementById('apiKey').value.trim();
      const dsn = document.getElementById('dsn').value.trim();
      const messageDiv = document.getElementById('message');
      const generateBtn = document.getElementById('generateBtn');
      
      // Clear previous messages
      messageDiv.innerHTML = '';
      
      // Validate inputs
      if (!apiKey) {
        showMessage('Please enter your API key', 'error');
        return;
      }
      
      if (!dsn) {
        showMessage('Please enter your DSN', 'error');
        return;
      }

      // Disable button during request
      generateBtn.disabled = true;
      generateBtn.textContent = 'Generating...';

      try {
        const response = await fetch(`${dsn}/api/v1/accounts`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-KEY': apiKey
          },
          body: JSON.stringify({ provider: 'WHATSAPP' })
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const data = await response.json();
        console.log('API Response:', data);

        // Handle different response structures
        let qrCodeString = null;

        if (data.qrCodeString) {
          qrCodeString = data.qrCodeString;
        } else if (data.checkpoint && data.checkpoint.qrcode) {
          qrCodeString = data.checkpoint.qrcode;
        } else if (data.qrcode) {
          qrCodeString = data.qrcode;
        } else if (data.qr_code) {
          qrCodeString = data.qr_code;
        }

        if (!qrCodeString) {
          // Check if account already exists
          if (data.object === 'Account') {
            throw new Error('Account already exists and is connected. No QR code needed.');
          } else {
            console.error('Full API Response:', JSON.stringify(data, null, 2));
            throw new Error('No QR code string received from API. Check console for full response.');
          }
        }
        const qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=250x250&data=${encodeURIComponent(qrCodeString)}`;

        document.getElementById('qrContainer').innerHTML = `
          <div>
            <img src="${qrImageUrl}" alt="Scan QR with WhatsApp" />
            <p>Scan this QR code with WhatsApp to connect your account</p>
            <p><small>Account ID: ${data.account_id || 'N/A'}</small></p>
          </div>
        `;
        
        showMessage('QR code generated successfully! Scan with WhatsApp to connect.', 'success');
        
      } catch (error) {
        console.error('Error generating QR code:', error);
        showMessage(`Failed to generate QR code: ${error.message}`, 'error');
        document.getElementById('qrContainer').innerHTML = '';
      } finally {
        // Re-enable button
        generateBtn.disabled = false;
        generateBtn.textContent = 'Generate QR Code';
      }
    }

    function showMessage(text, type) {
      const messageDiv = document.getElementById('message');
      messageDiv.textContent = text;
      messageDiv.className = type;
    }

    // Auto-focus on API key input
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('apiKey').focus();
    });
  </script>
</body>
</html>
