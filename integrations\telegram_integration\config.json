{"platform": "telegram", "provider": "TELEGRAM", "api_key": "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=", "dsn": "https://api1.unipile.com:13115", "authentication": {"method": "qr_code", "requires_mobile_app": true, "instructions": ["Open Telegram on your phone", "Go to Settings → Devices", "Tap 'Link Desktop Device'", "Scan the QR code"]}, "messaging": {"supports_bulk": true, "supports_channels": true, "supports_groups": true, "rate_limit_delay": 3, "max_message_length": 4096, "supported_formats": ["text", "image", "document", "video", "audio"], "personalization": {"supported_placeholders": ["{name}", "{username}", "{company}", "{phone}", "{email}", "{location}", "{industry}"]}}, "lead_integration": {"identifier_fields": ["username", "phone"], "required_fields": ["username"], "optional_fields": ["name", "company", "email", "location", "industry"], "validation": {"username_regex": "^@?[a-zA-Z0-9_]{5,32}$", "phone_regex": "^\\+[1-9]\\d{1,14}$"}}, "channel_settings": {"supports_broadcasting": true, "max_subscribers": "unlimited", "scheduling": {"enabled": true, "timezone": "UTC"}}, "campaign_settings": {"default_delay_seconds": 3, "max_daily_messages": 2000, "business_hours": {"enabled": true, "start_time": "09:00", "end_time": "21:00", "timezone": "UTC"}, "retry_settings": {"max_retries": 3, "retry_delay_minutes": 15}}, "templates": {"default_intro": "Hi {name}! Hope you're doing well.", "business_outreach": "Hello {name}, I found your profile and was impressed by your work at {company}. I'd love to discuss a potential collaboration.", "follow_up": "Hi {name}, following up on my previous message. Are you available for a quick chat about {company}?", "networking": "Hello {name}! I see we're both in the {industry} space. Would love to connect and share insights.", "channel_announcement": "🚀 Exciting news for our {industry} community! Check out this opportunity..."}, "bot_integration": {"supports_bots": true, "webhook_support": true, "inline_keyboards": true, "commands": {"enabled": true, "custom_commands": []}}, "logging": {"enabled": true, "log_level": "INFO", "log_file": "telegram_messaging.log", "include_message_content": false, "retention_days": 30}, "security": {"encrypt_logs": false, "mask_usernames": true, "audit_trail": true}}