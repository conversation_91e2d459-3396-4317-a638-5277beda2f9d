#!/usr/bin/env python3
"""
Interactive WhatsApp messaging test script.
"""

import sys
import os
import json
import time

# Add the integrations directory to the path
sys.path.append('integrations')

from whatsapp_integration.whatsapp_api import WhatsAppMessaging

def load_config():
    """Load API key from config file."""
    try:
        with open('integrations/whatsapp_integration/config.json', 'r') as f:
            config = json.load(f)
            return config.get('api_key')
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return None

def wait_for_authentication(whatsapp):
    """Wait for user to scan QR code and authenticate."""
    print("\n⏳ Waiting for authentication...")
    print("📱 Please scan the QR code with your WhatsApp mobile app")
    print("   1. Open WhatsApp on your phone")
    print("   2. Go to Settings → Linked Devices")
    print("   3. Tap 'Link a Device'")
    print("   4. Scan the QR code displayed above")
    
    # Wait for user confirmation
    input("\n✅ Press Enter after you've scanned the QR code...")
    
    # Check account status
    try:
        status = whatsapp.unipile.check_account_status()
        print(f"📊 Account Status: {status}")
        return True
    except Exception as e:
        print(f"❌ Error checking status: {e}")
        return False

def test_single_message(whatsapp):
    """Test sending a single message."""
    print("\n📤 Testing Single Message")
    print("=" * 30)
    
    # Get phone number from user
    phone = input("📞 Enter phone number (with country code, e.g., +**********): ").strip()
    if not phone:
        print("❌ No phone number provided")
        return
    
    # Get message from user
    message = input("💬 Enter message to send: ").strip()
    if not message:
        message = "Hello! This is a test message from WhatsApp API integration 🚀"
    
    try:
        print(f"\n📤 Sending message to {phone}...")
        result = whatsapp.send_message(phone, message)
        print("✅ Message sent successfully!")
        print(f"📋 Response: {result}")
        return True
    except Exception as e:
        print(f"❌ Failed to send message: {e}")
        return False

def test_bulk_messages(whatsapp):
    """Test sending bulk messages."""
    print("\n📤 Testing Bulk Messages")
    print("=" * 30)
    
    # Get phone numbers from user
    print("📞 Enter phone numbers (one per line, with country code)")
    print("   Example: +**********")
    print("   Press Enter twice when done:")
    
    recipients = []
    while True:
        phone = input("Phone: ").strip()
        if not phone:
            break
        recipients.append(phone)
    
    if not recipients:
        print("❌ No phone numbers provided")
        return
    
    # Get message from user
    message = input("💬 Enter message to send to all recipients: ").strip()
    if not message:
        message = "Hello! This is a bulk test message from WhatsApp API integration 🚀"
    
    try:
        print(f"\n📤 Sending bulk message to {len(recipients)} recipients...")
        results = whatsapp.send_bulk_messages(recipients, message)
        print("✅ Bulk messages sent!")
        
        # Show results for each recipient
        for phone, result in results.items():
            status = "✅ Success" if result.get('success') else "❌ Failed"
            print(f"   {phone}: {status}")
            if not result.get('success'):
                print(f"      Error: {result.get('error')}")
        
        return True
    except Exception as e:
        print(f"❌ Failed to send bulk messages: {e}")
        return False

def main():
    """Main interactive test function."""
    print("🧪 WhatsApp Messaging Test")
    print("=" * 50)
    
    # Load API key
    api_key = load_config()
    if not api_key:
        print("❌ No API key found in config.json")
        return
    
    print(f"✅ API key loaded: {api_key[:10]}...")
    
    try:
        # Initialize WhatsApp
        print("\n1️⃣ Initializing WhatsApp...")
        whatsapp = WhatsAppMessaging(unipile_api_key=api_key)
        print("✅ WhatsApp initialized!")
        
        # Authenticate
        print("\n2️⃣ Authenticating account...")
        auth_result = whatsapp.authenticate_account()
        print(f"📱 Account ID: {auth_result.get('account_id')}")
        
        # Display QR code info
        qr_code = auth_result.get('qr_code')
        if qr_code:
            print(f"🔗 QR Code generated (length: {len(qr_code)} characters)")
            print("💡 You can also use the web interface to see the QR code visually")
        
        # Wait for authentication
        if not wait_for_authentication(whatsapp):
            print("❌ Authentication failed or not completed")
            return
        
        # Test messaging
        while True:
            print("\n🎯 What would you like to test?")
            print("1. Send single message")
            print("2. Send bulk messages")
            print("3. Exit")
            
            choice = input("\nEnter choice (1-3): ").strip()
            
            if choice == "1":
                test_single_message(whatsapp)
            elif choice == "2":
                test_bulk_messages(whatsapp)
            elif choice == "3":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please enter 1, 2, or 3.")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
