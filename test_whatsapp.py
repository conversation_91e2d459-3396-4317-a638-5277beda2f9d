#!/usr/bin/env python3
"""
Test script for WhatsApp integration using your desired structure.
"""

import sys
import os
import json

# Add the integrations directory to the path
sys.path.append('integrations')

from whatsapp_integration.whatsapp_api import WhatsAppMessaging

def load_config():
    """Load API key from config file."""
    try:
        with open('integrations/whatsapp_integration/config.json', 'r') as f:
            config = json.load(f)
            return config.get('api_key')
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return None

def test_whatsapp_structure():
    """Test the WhatsApp messaging structure you requested."""
    print("🧪 Testing WhatsApp Integration Structure")
    print("=" * 50)
    
    # Load API key from config
    api_key = load_config()
    if not api_key:
        print("❌ No API key found in config.json")
        return
    
    print(f"✅ API key loaded: {api_key[:10]}...")
    
    try:
        # Test your desired structure
        print("\n1️⃣ Testing WhatsAppMessaging initialization...")
        whatsapp = WhatsAppMessaging(unipile_api_key=api_key)
        print("✅ WhatsAppMessaging initialized successfully!")
        
        print("\n2️⃣ Testing authenticate_account() method...")
        auth_result = whatsapp.authenticate_account()
        print("✅ Authentication method called successfully!")
        print(f"📱 Account ID: {auth_result.get('account_id')}")
        print(f"🔗 QR Code: {auth_result.get('qr_code')[:50]}..." if auth_result.get('qr_code') else "No QR code")
        
        print("\n3️⃣ Testing send_message() method structure...")
        # Note: We won't actually send a message without a valid phone number
        print("✅ send_message(phone_number, message) method available")
        
        print("\n4️⃣ Testing send_bulk_messages() method structure...")
        print("✅ send_bulk_messages(recipients, message) method available")
        
        print("\n🎉 All tests passed! Your WhatsApp structure is working correctly.")
        
        # Show available methods
        print("\n📋 Available methods:")
        methods = [method for method in dir(whatsapp) if not method.startswith('_')]
        for method in methods:
            print(f"   • {method}()")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

def test_command_line():
    """Test command line interface."""
    print("\n🖥️ Testing Command Line Interface")
    print("=" * 50)
    
    print("Available commands:")
    print("  python whatsapp_api.py generate_qr [save_path]")
    print("  python whatsapp_api.py check_status [account_id]") 
    print("  python whatsapp_api.py send_message <to> <message> [account_id]")

if __name__ == "__main__":
    test_whatsapp_structure()
    test_command_line()
    
    print("\n🌐 Web Interface:")
    print("  Open integrations/whatsapp_integration/whatsapp.html in your browser")
    print("  Enter your API key and click 'Generate QR Code'")
