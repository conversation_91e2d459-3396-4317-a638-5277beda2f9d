{"platform": "facebook", "provider": "FACEBOOK", "api_key": "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=", "dsn": "https://api1.unipile.com:13115", "authentication": {"method": "email_password", "requires_credentials": true, "instructions": ["Enter your Facebook email or username", "Enter your Facebook password", "Click 'Connect Facebook'", "Wait for authentication confirmation"]}, "messaging": {"supports_messenger": true, "supports_bulk": true, "rate_limit_delay": 10, "max_message_length": 8000, "supported_formats": ["text", "image", "link"], "personalization": {"supported_placeholders": ["{name}", "{username}", "{company}", "{email}", "{location}", "{industry}", "{mutual_friends}"]}}, "lead_integration": {"identifier_fields": ["facebook_username", "username"], "required_fields": ["facebook_username"], "optional_fields": ["name", "company", "email", "location", "industry"], "validation": {"username_regex": "^[a-zA-Z0-9.]{1,50}$"}}, "page_management": {"supports_pages": true, "can_post_content": true, "can_manage_comments": true, "analytics_available": true}, "campaign_settings": {"default_delay_seconds": 10, "max_daily_messages": 500, "business_hours": {"enabled": true, "start_time": "09:00", "end_time": "18:00", "timezone": "UTC"}, "retry_settings": {"max_retries": 2, "retry_delay_minutes": 60}}, "templates": {"default_intro": "Hi {name}! I hope you're doing well.", "business_outreach": "Hello {name}, I came across your profile and was impressed by your work at {company}. I'd love to discuss a potential collaboration opportunity.", "follow_up": "Hi {name}, following up on my previous message about {company}. Would you be interested in a quick call?", "networking": "Hello {name}! I see we have {mutual_friends} mutual friends. I'd love to connect and share insights about {industry}.", "page_post": "🚀 Exciting news for our {industry} community! Check out this amazing opportunity..."}, "compliance": {"respect_privacy_settings": true, "avoid_spam_behavior": true, "follow_community_standards": true, "rate_limiting_enabled": true}, "logging": {"enabled": true, "log_level": "INFO", "log_file": "facebook_messaging.log", "include_message_content": false, "retention_days": 30}, "security": {"encrypt_logs": false, "mask_usernames": true, "audit_trail": true, "secure_credential_handling": true}}